﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Skybridge.Domain.Core;
using Skybridge.Robot.Presentation.ViewModels;
using Skybridge.Robot.Presentation.ViewModels.AboutPage;
using Skybridge.Robot.Presentation.ViewModels.ProjectPage;
using Skybridge.Robot.Presentation.ViewModels.SettingPage;
using Skybridge.Robot.Presentation.ViewModels.TaskPage;

namespace Skybridge.Robot.Presentation.Services
{
    public class PageService
    {
        private readonly ObservableCollection<Router>? _routers;
        private readonly IList<IPage> _pages;

        public PageService(TaskPageImpl taskPageImpl, AboutPageImpl aboutPageImpl, ProjectPageImpl projectPageImpl)
        {
            _routers = new();
            _pages = new List<IPage>
            {
                projectPageImpl,
                taskPageImpl,
                aboutPageImpl
            };
        }

        public void Load(string name)
        {
            _pages.FirstOrDefault(x => x.Name.Equals(name)).Load();
        }

        public ObservableCollection<Router>? GetPages()
        {
            if (_pages != null)
                foreach (var page in _pages)
                {
                    _routers.Add(new()
                    {
                        Name = page.Name,
                        View = page.View,
                        Icon = page.Icon
                    });
                }

            return _routers;
        }
    }
}