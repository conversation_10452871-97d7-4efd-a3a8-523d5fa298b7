﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Application.DTOs.Task.Responses
{
    public class GetTaskResponse
    {
        /// <summary>
        ///     任务Id,唯一标识
        /// </summary>
        [JsonProperty("task_id")]
        public string Id { get; set; }
        [JsonProperty("code")]
        public string Code { get; set; }
        /// <summary>
        /// 项目Id
        /// </summary>
        [JsonProperty("projectId")]
        public string ProjectId { get; set; }

        [JsonProperty("token")] 
        public string Token { get; set; }

        [JsonProperty("parameters")] 
        public string Parameters { get; set; }

        [JsonProperty("name")] 
        public string ProjectName { get; set; }

        [JsonProperty("version")] 
        public string Version { get; set; }

        /// <summary>
        ///     作业Id
        /// </summary>
        [JsonProperty("project_content_id")]
        public string ProjectContentId { get; set; }

        [JsonProperty("description")] 
        public string Description { get; set; }

        [JsonProperty("is_video_log")]
        public bool IsVideoLog { get; set; }

        [JsonProperty("log_type")] 
        public string LogType { get; set; }
        
        [JsonProperty("label")]
        public string Label { get; set; }
        
        /// <summary>
        /// 任务类型 1.普通项目 2.分组项目 3.编排项目
        /// </summary>
        [JsonProperty("category")]
        public int Category { get; set; }
        
        [JsonProperty("parentId")]
        public string ParentId { get; set; }
        
        [JsonProperty("projectRoot")]
        public string ProjectRoot { get; set; }
        
        public bool IsSingleTask()
        {
            bool isSingleTask = false;
            if (Category == 1 || Category == 0)
            {
                isSingleTask = true;
            }
            else
            {
                if (string.IsNullOrEmpty(ParentId))
                {
                    isSingleTask = false;
                }
                else
                {
                    isSingleTask = true;
                }
            }
            return isSingleTask;
        }
    }
}