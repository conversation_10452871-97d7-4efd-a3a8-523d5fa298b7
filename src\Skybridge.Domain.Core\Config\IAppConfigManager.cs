﻿using Skybridge.Domain.Core.Enums;
using System;

namespace Skybridge.Domain.Core.Config;

public interface IAppConfigManager
{
    Action<string> OnChangeServerUrl { get; set; }
    Action<string> OnChangeRobotName { get; set; }
    Action OnChangeTaskMode { get; set; }
    RobotConfig RobotConfig { get; }
    ApplicaitonConfig ApplicationConfig { get; }
    Action<Mode> OnChangeMode { get; set; }

    void Save(string serverUrl, string robotName, bool isServerMode, RobotTaskMode robotTaskMode);
}