﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Domain.Core.Models
{
    /// <summary>
    /// 任务日志模型
    /// </summary>
    public class TaskLogModel
    {
        [JsonProperty("task_id")] public string Task_Id { get; set; }

        [JsonProperty("rpa_id")] public string RpaId { get; set; }

        [JsonProperty("component_name")] public string Component_Name { get; set; }

        /// <summary>
        /// 0 执行中 1 执行成功 2执行失败
        /// </summary>
        [JsonProperty("state")]
        public int State { get; set; }

        [JsonProperty("detail")] public string Details { get; set; }
    }
}