﻿<components:RobotWindow xmlns="https://github.com/avaloniaui"
                        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                        xmlns:components="clr-namespace:Skybridge.Robot.Styles.Components;assembly=Skybridge.Robot.Styles"
                        xmlns:avalonia="https://github.com/projektanker/icons.avalonia"
                        xmlns:views="clr-namespace:Skybridge.Robot.Desktop.Views"
                        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
                        x:Class="Skybridge.Robot.Desktop.Views.LicenseView"
                        Title="{Binding Title}"
                        Width="600"
                        Height="430"
                        WindowStartupLocation="CenterOwner"
                        HeaderBackground="#3389F1"
                        CanResize="False"
                        IsMaxVisible="False"
                        Foreground="Azure"
                        IsMinVisible="False"
                        Icon="../Assets/logo.png"
                        HeaderIcon="../Assets/trayicon.png"
                        x:DataType="views:LicenseViewModel"
                        Classes="header">
    <Window.Resources>
            <views:QrCodeConvert x:Key="ImageConvert">
            </views:QrCodeConvert>
    </Window.Resources>

    <Design.DataContext>
        <views:LicenseViewModel />
    </Design.DataContext>
    <StackPanel Margin="25,20">
        <StackPanel Orientation="Horizontal">
            <avalonia:Icon VerticalAlignment="Center" FontSize="30" Foreground="Red" Value="rti-warning" />
            <TextBlock VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="Red" FontWeight="Bold"
                       Text="{Binding Message}"
                       TextWrapping="Wrap" FontSize="16" Margin="10" />
        </StackPanel>
        <Image Source="{Binding MachineCode,Converter={StaticResource ImageConvert}}" Height="200" Width="200" />
        <StackPanel Margin="10" Orientation="Horizontal" HorizontalAlignment="Center">
            <Label DockPanel.Dock="Left" Foreground="Black" Content="机器码" VerticalAlignment="Center" FontWeight="Bold"
                   Margin="10,0" />
            <TextBox HorizontalAlignment="Stretch"
                     IsReadOnly="True"
                     CornerRadius="3"
                     BorderThickness="0"
                     Background="Transparent"
                     Text="{Binding MachineCode}" VerticalAlignment="Center" />
        </StackPanel>
        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
            <Button
                Theme="{StaticResource BorderButton}"
                Padding="10"
                Click="Cancel"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal" Spacing="10">
                    <!-- <avalonia:Icon VerticalAlignment="Center" Value="rti-download"></avalonia:Icon> -->
                    <TextBlock VerticalAlignment="Center">取消</TextBlock>
                </StackPanel>
            </Button>
            <Button
                Padding="10"
                Classes="Primary"
                Theme="{DynamicResource SolidButton}"
                Click="ImportLicense"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal" Spacing="10">
                    <!-- <avalonia:Icon VerticalAlignment="Center" Value="rti-download"></avalonia:Icon> -->
                    <TextBlock VerticalAlignment="Center">安装证书</TextBlock>
                </StackPanel>
            </Button>
        </StackPanel>
    </StackPanel>
</components:RobotWindow>