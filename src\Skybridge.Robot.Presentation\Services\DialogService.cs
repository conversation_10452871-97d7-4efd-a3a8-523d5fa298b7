﻿using System.Threading.Tasks;
using Avalonia.Controls;
using FluentAvalonia.UI.Controls;

namespace Skybridge.Robot.Presentation.Services;

public class DialogService
{
    public DialogService()
    {
        // Initialize any required services or settings here
    }

    public  async Task<ContentDialogResult> ShowMessageAsync(string title,string message,TopLevel? topLevel= null)
    {
        ContentDialog cd = new ContentDialog()
        {
            Title = title,
            Content = message,
            CloseButtonText = "确认"
            
        };
        // Show the dialog 
        if (topLevel != null)
        {
            return await cd.ShowAsync(topLevel);
        }

        return await cd.ShowAsync();
    }
    
    public async Task<ContentDialogResult> ShowConfirmAsync(string title, string message, TopLevel? topLevel = null)
    {
        ContentDialog cd = new ContentDialog()
        {
            Title = title,
            Content = message,
            PrimaryButtonText = "确认",
            CloseButtonText = "取消"
        };
        
        // Show the dialog 
        if (topLevel != null)
        {
            return await cd.ShowAsync(topLevel);
        }

        return await cd.ShowAsync();
    }
}