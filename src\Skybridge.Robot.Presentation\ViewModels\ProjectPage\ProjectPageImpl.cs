﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Avalonia.Controls;
using Skybridge.Domain.Core;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Presentation.Services;
using Skybridge.Robot.Utils;

namespace Skybridge.Robot.Presentation.ViewModels.ProjectPage
{
    public class ProjectPageImpl : IPage
    {
        public string Name => "项目";
        public int Order => 0;
        public string Icon => "rti-project";

        public void Load()
        {
        }

        public UserControl View { get; }

        private readonly IProjectManager _projectManager;

        private readonly ProjectViewModel _projectViewModel;

        private readonly IExecManager _execManager;

        private readonly ITaskManager _taskManager;
        private readonly RobotContext _robotContext;
        private readonly IBusinessRequestService _businessRequestService;
        private readonly RunningContext _runningContext;
        private readonly DialogService _dialogService;
        private readonly ILogService _logService;
        public ProjectPageImpl(IProjectManager projectManager, IExecManager execManager, ITaskManager taskManager,
            RobotContext robotContext,IBusinessRequestService businessRequestService,RunningContext runningContext,
            DialogService dialogService,IAppConfigManager appConfigManager,ILogService logService)
        {
            _businessRequestService = businessRequestService;
            _robotContext = robotContext;
            _projectManager = projectManager;
            _execManager = execManager;
            _taskManager = taskManager;
            _logService = logService;
            _runningContext = runningContext;
            _dialogService = dialogService;
            _projectViewModel = new();
            appConfigManager.OnChangeMode += async void (mode) =>
            {
                try
                {
                    _projectViewModel.ProjectItems.Clear();
                    await RealodProjectList();
                }
                catch (Exception ex)
                {
                    _logService.LogError("项目列表加载失败", ex);
                }
            };
            _runningContext.OnProjectAdded += AddProjectItem;
            _ = RealodProjectList();
            View = new ProjectView
            {
                DataContext = _projectViewModel
            };


        }

        /// <summary>
        /// 当离线模式和在线模式切换时，重新加载项目列表
        /// </summary>
        public async Task RealodProjectList()
        {
            _projectManager.LoadList()
                .Where(d => d.Item1.IsRemote == _robotContext.IsRemote)
                .EachDo(AddProjectItem);
        }

        private void ProjectAdded(RobotProject obj)
        {
            throw new NotImplementedException();
        }

        private void AddProjectItem(RobotProject project)
        {
            if (!_projectViewModel.ProjectItems.Any(x => x.Name.Equals(project.Name) && x.Icon.Equals("rti-remote") == project.IsRemote))
            {
                var pItem = new ProjectItemViewModel(project,_projectManager, _execManager, _taskManager,_robotContext,
                    _businessRequestService,_dialogService,_runningContext, _logService)
                {
                    Name = project.Name,
                    Versions = new ObservableCollection<Tuple<string, string>> { new(project.Id,project.Version) },
                    Icon = project.IsRemote ? "rti-remote" : "rti-import",
                    Version = new(project.Id,project.Version),
                };
                pItem.DelProjectCommand.Subscribe(result =>
                {
                    if (result)
                        DelChangeVersion(pItem);
                });
                _projectViewModel.ProjectItems.Add(pItem);
            }
            else
            {
                var pItem = _projectViewModel.ProjectItems.FirstOrDefault(x => x.Name.Equals(project.Name));
                pItem.Versions.Add(new(project.Id,project.Version));
                pItem.Version = new(project.Id,project.Version);
            }
        }

        private void AddProjectItem((RobotProject, IEnumerable<Tuple<string, string>>) x)
        {
            var pItem = new ProjectItemViewModel(x.Item1,_projectManager, _execManager, _taskManager,_robotContext,
                _businessRequestService,_dialogService,_runningContext, _logService)
            {
                Name = x.Item1.Name,
                Versions = new(x.Item2),
                Icon = x.Item1.IsRemote ? "rti-remote" : "rti-import",
                Version = x.Item2.Last(),
            };
            pItem.DelProjectCommand.Subscribe(result =>
            {
                if (result)
                    DelChangeVersion(pItem);
            });
            _projectViewModel.ProjectItems.Add(pItem);
        }

        private void DelChangeVersion(ProjectItemViewModel pItem)
        {
            _projectManager.DelProject(pItem.Version.Item1);
            var versions = _projectViewModel.ProjectItems.FirstOrDefault(x => x.Version.Equals(pItem.Version)).Versions;
            if (versions.Count > 1)
            {
                versions.Remove(versions.FirstOrDefault(x => x.Item1.Equals(pItem.Version.Item1)));
                pItem.Version = versions.Last();
            }
            else
                _projectViewModel.ProjectItems.Remove(
                    _projectViewModel.ProjectItems.FirstOrDefault(x => x.Version.Equals(pItem.Version)));
        }
    }
}