﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net6.0</TargetFramework>
        <Nullable>enable</Nullable>
        <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
        <AssemblyName>Skybridge.Robot</AssemblyName>
		<RootNamespace>Skybridge.Robot</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <OutputPath>..\..\Output\Debug</OutputPath>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <OutputPath>..\..\Output\Release</OutputPath>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\CommonCore\src\Skybridge.Controls\Skybridge.Controls.csproj" />
        <ProjectReference Include="..\..\..\CommonCore\src\Skybridge.Theme\Skybridge.Theme.csproj" />
        <ProjectReference Include="..\Skybridge.Domain.Core\Skybridge.Domain.Core.csproj" />
        <ProjectReference Include="..\Skybridge.Robot.CommonView\Skybridge.Robot.CommonView.csproj" />
        <ProjectReference Include="..\Skybridge.Robot.Infrastructure\Skybridge.Robot.Infrastructure.csproj" />
        <ProjectReference Include="..\Skybridge.Robot.Presentation\Skybridge.Robot.Presentation.csproj" />
        <ProjectReference Include="..\Skybridge.Robot.Styles\Skybridge.Robot.Styles.csproj" />
        <ProjectReference Include="..\Skybridge.Robot.Utils\Skybridge.Robot.Utils.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="appsettings.json" />
      <Content Include="appsettings.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>
	<ItemGroup>
		<PackageReference Include="HotAvalonia" Version="3.0.0" PrivateAssets="All" Publish="True" />
	</ItemGroup>

    <ItemGroup>
      <None Update="appVersion.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <None Include="logo.png">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>
</Project>
