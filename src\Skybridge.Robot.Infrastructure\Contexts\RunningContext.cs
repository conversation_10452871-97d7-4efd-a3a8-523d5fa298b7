﻿using System.Collections.Concurrent;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.Contexts
{
    public class RunningContext
    {
        private readonly ILogService _logService;


        /// <summary>
        /// 项目列表缓存
        /// </summary>
        public List<RobotProject> RobotProjects { get; } = new List<RobotProject>();

        public ConcurrentDictionary<string, RobotTask> RobotTasks { get; } = new ConcurrentDictionary<string, RobotTask>();

        //public ConcurrentDictionary<string, RobotTask> TaskLogUploadDict { get; } = new ConcurrentDictionary<string, RobotTask>();
        public Action<RobotTask> OnTaskAdded { get; set; }

        public Action<RobotTask> OnTaskCompleted { get; set; }
        public Action<RobotProject> OnProjectAdded { get; set; }
        
        public Action<RobotProject> OnProjectUpdated { get; set; }
        public Action TaskQueueEmpty { get; set; }
        public ConcurrentDictionary<string, RobotTask> TaskLogUploadDict { get; } = new ConcurrentDictionary<string, RobotTask>();
        public Action<int> OnTaskCountChanged { get; set; }
        
        /// <summary>
        /// 任务日志更新完成
        /// </summary>
        public Action<RobotTask?, string> OnTaskLogEndUpdated { get; set; }

        public Action TaskQueueNotEmpty { get; set; }

        public RunningContext(JsonDbContext jsonDbContext,ILogService logService)
        {
            _logService = logService;
            var projs = jsonDbContext.Get<RobotProject>();
            foreach (var item in projs)
            {
                RobotProjects.Add(item);
            }
            OnTaskCompleted += OnCompleted;
            OnTaskAdded += (robotTask) =>
            {
                OnTaskCountChanged?.Invoke(RobotTasks.Count);
            };
            OnTaskCountChanged += (count) =>
            {
                if (count == 0)
                {
                    TaskQueueEmpty?.Invoke();
                }
                else
                {
                    TaskQueueNotEmpty?.Invoke();
                }
            };
            StartTaskLogUploadDictClean();
        }
        public bool CanAddTask(string projectId, string taskId, out string reason)
        {
            reason = string.Empty;

            if (RobotTasks.Any(d => d.Key.Equals(taskId)))
            {
                reason = $"该任务{taskId}已存在，不可以重复添加";
                return false;
            }
            return true;
        }
        /// <summary>
        /// 任务完成后处理
        /// </summary>
        /// <param name="robotTask"></param>
        private void OnCompleted(RobotTask robotTask)
        {
            string taskId = robotTask.Id;
            var task = RobotTasks.FirstOrDefault(d => d.Key.Equals(taskId)).Value;

            if (task != null)
            {
                task.Status = 1;
                bool bRes = RobotTasks.TryRemove(taskId, out _);
                robotTask.LogUploadTimer.Restart();
                if (bRes)
                {
                    OnTaskCountChanged?.Invoke(RobotTasks.Count);
                }
            }
        }
        
        private void StartTaskLogUploadDictClean(int intervalSeconds = 20)
        {
            // 使用 TaskCreationOptions.LongRunning 标记为长线程任务
            Task.Factory.StartNew(async () =>
            {
                while (true)
                {
                    if (TaskLogUploadDict.Count == 0)
                    {
                        await Task.Delay(3000);
                        continue;
                    }

                    foreach (var item in TaskLogUploadDict)
                    {
                        Console.WriteLine(item.Value.LogUploadTimer.ElapsedMilliseconds);
                        if ((item.Value.LogUploadTimer.ElapsedMilliseconds) > intervalSeconds * 1000)
                        {
                            TaskLogUploadDict.TryRemove(item.Key, out _);
                            _logService.LogInfo($"任务{item.Key}超过:{intervalSeconds}秒没有收到日志，自动清理");
                        }
                    }

                    await Task.Delay(3000);
                }
            }, TaskCreationOptions.LongRunning); // 标记为长时间运行任务
        }
    }
}