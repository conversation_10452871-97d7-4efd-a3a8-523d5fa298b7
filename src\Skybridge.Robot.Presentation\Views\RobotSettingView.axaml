<UserControl
    x:Class="Skybridge.Robot.Presentation.RobotSettingView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converts="clr-namespace:Skybridge.Robot.Presentation.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:enums="clr-namespace:Skybridge.Domain.Core.Enums;assembly=Skybridge.Domain.Core"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:Skybridge.Robot.Presentation.ViewModels.RobotSetting"
    d:DataContext="{vm:RobotSettingViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="vm:RobotSettingViewModel"
    mc:Ignorable="d">
	<UserControl.Resources>
		<converts:EnumToBooleanConverter x:Key="EnumToBooleanConverter" />
	</UserControl.Resources>
	<Grid Margin="20">
		<StackPanel Spacing="10">
			<!--  远程模式  -->
			<StackPanel VerticalAlignment="Center" Orientation="Horizontal">
				<TextBlock
                    Margin="0,0,10,0"
                    VerticalAlignment="Center"
                    Text="远程模式：" />
				<ToggleSwitch
                    VerticalAlignment="Center"
                    IsChecked="{Binding IsRemoteMode}"
                    OffContent="离线"
                    OnContent="在线" />
			</StackPanel>

			<!--  调度模式  -->
			<StackPanel
                VerticalAlignment="Center"
                IsVisible="{Binding IsRemoteMode}"
                Orientation="Horizontal">
				<TextBlock
                    Margin="0,0,10,0"
                    VerticalAlignment="Center"
                    Text="调度模式：" />
				<RadioButton
                    VerticalAlignment="Center"
                    Content="轮询"
                    GroupName="DispatchMode"
                    IsChecked="{Binding TaskMode, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static enums:RobotTaskMode.PollingMode}, Mode=TwoWay}" />
				<RadioButton
                    Margin="10,0,0,0"
                    VerticalAlignment="Center"
                    Content="推送"
                    GroupName="DispatchMode"
                    IsChecked="{Binding TaskMode, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static enums:RobotTaskMode.WebsocketMode}, Mode=TwoWay}" />
			</StackPanel>

			<!--  服务器地址  -->
			<StackPanel
                VerticalAlignment="Center"
                IsVisible="{Binding IsRemoteMode}"
                Orientation="Horizontal">
				<TextBlock
                    Margin="0,0,10,0"
                    VerticalAlignment="Center"
                    Text="服务器地址：" />
				<TextBox
                    Width="200"
                    VerticalAlignment="Center"
                    Text="{Binding ServerAddress}" />
				<Button
                    Margin="10,0,0,0"
                    VerticalAlignment="Center"
                    Classes="accent"
                    Command="{Binding TestConnectionCommand}"
                    Content="测试连接" />
			</StackPanel>

			<!--  机器人名称  -->
			<StackPanel
                VerticalAlignment="Center"
                IsVisible="{Binding IsRemoteMode}"
                Orientation="Horizontal">
				<TextBlock
                    Margin="0,0,10,0"
                    VerticalAlignment="Center"
                    Text="机器人名称：" />
				<TextBox
                    Width="290"
                    VerticalAlignment="Center"
                    Text="{Binding RobotName}" />
			</StackPanel>
		</StackPanel>
	</Grid>
</UserControl>