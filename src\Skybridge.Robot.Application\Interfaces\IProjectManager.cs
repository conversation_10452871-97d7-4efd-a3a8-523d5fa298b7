﻿using Skybridge.Domain.Core.Model;
using Skybridge.Domain.Core.Models;

namespace Skybridge.Robot.Application.Interfaces
{
    public interface IProjectManager
    {

        IEnumerable<(RobotProject, IEnumerable<Tuple<string, string>>)> LoadList();

        void ChangeArguments(string projectName, string version, IEnumerable<ArgumentModel> arguments);

        IEnumerable<ArgumentModel>? GetProjectRunArguments(string pId);

        void DelProject(string id);
    }
}