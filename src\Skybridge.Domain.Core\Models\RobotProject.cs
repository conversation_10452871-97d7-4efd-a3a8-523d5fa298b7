using Avalonia.OpenGL;
using System;
using System.Collections.Generic;
using System.IO;

namespace Skybridge.Domain.Core.Models;

/// <summary>
/// 机器人项目模型
/// </summary>
public class RobotProject : IEntity
{
    /// <summary>
    /// 本地的时候使用项目文件中的projectid，远程的时候使用项目服务端返回的id
    /// </summary>
    /// 
    public string Id { get; set; }
    /// <summary>
    /// project_content_id 标识项目唯一的版本号
    /// </summary>
    public string ProjectContentId { get; }
    

    /// <summary>
    /// 项目名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 项目代码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 项目版本
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    /// 是否为远程模式
    /// </summary>
    public bool IsRemote { get; set; }

    /// <summary>
    /// 项目文件路径
    /// </summary>
    public string FilePath { get; set; }
    
    public string ZipFilePath
    {
        get
        {
            return FilePath + ".zip";
        }
    }
    public string Description { get; }
    public List<string> Tags { get; }
    
    public List<string> Versions { get; set; } = new List<string>();
    public List<string> FilePaths { get; set; } = new List<string>();
    public string ParametersPath => Path.Combine(FilePath, "parameters.json");
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    #region Multiple Task

    
    public int Category { get; }
    
    public string ParentId { get; }
    
    #endregion


    public RobotProject(string id, string projectContentId, bool isRemote, string code, string name, 
        string version, string filePath,string description,
        List<string> tags,int category, string parentId)
    {
        Id = id;
        ProjectContentId = projectContentId;
        IsRemote = isRemote;
        Code = code;
        Name = name;
        Version = version;
        FilePath = filePath;
        Description = description;
        Tags = tags;
        Category = category;
        ParentId = parentId;
    }
}