<UserControl
    x:Class="Skybridge.Robot.Presentation.SettingView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="using:Skybridge.Controls.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:enums="using:Skybridge.Domain.Core.Enums"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:settingPage="clr-namespace:Skybridge.Robot.Presentation.ViewModels.SettingPage"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="settingPage:SettingViewModel"
    mc:Ignorable="d">
	<UserControl.Resources>
		<converter:EnumToBooleanConverter x:Key="EnumToBooleanConverter" />
	</UserControl.Resources>
	<DockPanel Margin="25" LastChildFill="True">
		<StackPanel DockPanel.Dock="Top">
			<TextBlock
                FontSize="17"
                FontWeight="Bold"
                Text="基础设置" />
			<Grid Margin="20" ColumnDefinitions="100,*">
				<TextBlock
                    Grid.Column="0"
                    Margin="0,0,10,0"
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="机器人名称:"
                    TextAlignment="Right" />
				<TextBox Grid.Column="1" Text="{Binding RobotName}">
					<Interaction.Behaviors>
						<EventTriggerBehavior EventName="LostFocus">
							<InvokeCommandAction Command="{Binding OnChangeRobotName}" />
						</EventTriggerBehavior>
					</Interaction.Behaviors>
				</TextBox>
			</Grid>
			<Grid Margin="20" ColumnDefinitions="100,*">
				<TextBlock
                    Grid.Column="0"
                    Margin="0,0,10,0"
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="调度器地址:"
                    TextAlignment="Right" />
				<TextBox
                    Grid.Column="1"
                    IsEnabled="{Binding !Mode}"
                    Text="{Binding ServerUrl}">
					<Interaction.Behaviors>
						<EventTriggerBehavior EventName="LostFocus">
							<InvokeCommandAction Command="{Binding OnChangeServerUrl}" />
						</EventTriggerBehavior>
					</Interaction.Behaviors>
				</TextBox>
			</Grid>
			<Grid Margin="20" ColumnDefinitions="100,*">
				<TextBlock
                    Grid.Column="0"
                    Margin="0,0,10,0"
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="远程调度:"
                    TextAlignment="Right" />
				<ToggleSwitch
                    Grid.Column="1"
                    IsChecked="{Binding Mode}"
                    IsEnabled="{Binding !ServerUrlHasError}"
                    OffContent="关"
                    OnContent="开">
					<Interaction.Behaviors>
						<EventTriggerBehavior EventName="IsCheckedChanged">
							<InvokeCommandAction Command="{Binding OnChangeMode}" />
						</EventTriggerBehavior>
					</Interaction.Behaviors>
				</ToggleSwitch>
			</Grid>
			<Grid Margin="20" ColumnDefinitions="100,*">
				<TextBlock
                    Grid.Column="0"
                    Margin="0,0,10,0"
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="调度模式:"
                    TextAlignment="Right" />
				<StackPanel Grid.Column="1" Orientation="Horizontal">
					<RadioButton
                        Content="轮询"
                        GroupName="TaskModeGroup"
                        IsChecked="{Binding SelectedRobotTaskMode, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static enums:RobotTaskMode.PollingMode}}" />
					<RadioButton
                        Margin="10,0,0,0"
                        Content="推送"
                        GroupName="TaskModeGroup"
                        IsChecked="{Binding SelectedRobotTaskMode, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static enums:RobotTaskMode.WebsocketMode}}" />
				</StackPanel>
			</Grid>
		</StackPanel>
		<!--  <Button Width="45" Height="35" HorizontalAlignment="Right" VerticalAlignment="Bottom"  -->
		<!--  IsEnabled="{Binding !HasErrors}"  -->
		<!--  Theme="{StaticResource AccentButton}" Command="{Binding ConfigSave}">  -->
		<!--  保存  -->
		<!-- </Button> -->
	</DockPanel>
</UserControl>