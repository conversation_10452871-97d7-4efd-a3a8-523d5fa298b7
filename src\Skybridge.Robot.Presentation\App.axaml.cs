using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using Autofac;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using Gdk;
using Gtk;
using Skybridge.Domain.Core;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Exector;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Contexts;
using Thread = System.Threading.Thread;
using WindowState = Avalonia.Controls.WindowState;

namespace Skybridge.Robot.Presentation
{
    public partial class App : Avalonia.Application
    {
        private RobotContext? _robotContext;
        private IContainer? _container;

        public IContainer? Container => _container;

        public override void Initialize()
        {
            AvaloniaXamlLoader.Load(this);
        }

        public override void OnFrameworkInitializationCompleted()
        {
            try
            {
                var startup = new Startup();
                _container = startup.Start();
                _container.Resolve<IPluginManager>().SetContainer(_container);
                _container.Resolve<Bootstrap>().Run();
                _robotContext = _container.Resolve<RobotContext>();
                var lostDataContext = _container.Resolve<LostDataContext>();
                lostDataContext.StartLostDataUploadTask();
                var logService = _container.Resolve<ILogService>();
                var exeManager = _container.Resolve<IExecManager>();
                exeManager.InitializeServer();
                var uploadLogContext = _container.Resolve<UploadLogContext>();
                uploadLogContext.StartProcessLog();
                base.OnFrameworkInitializationCompleted();
                AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
                {
                    Console.WriteLine(args.ExceptionObject);
                    if (args.ExceptionObject is Exception ex)
                    {
                        logService.LogError( "Unhandled exception occurred",ex);
                    }
                    else
                    {
                        logService.LogError($"Unhandled exception occurred: {args.ExceptionObject.ToString()}");
                    }
                };
                AppDomain.CurrentDomain.ProcessExit += CurrentDomainOnProcessExit;
                CreateTrayIcon();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

        private bool IsUos()
        {
            string osReleaseFile = "/etc/os-release";
            if (File.Exists(osReleaseFile))
            {
                string[] lines = File.ReadAllLines(osReleaseFile);
                foreach (string line in lines)
                {
                    if (line.ToLower().Contains("uos") || line.Contains("uniontech"))
                    {
                        return true;
                    }
                }

                return false;
            }
            else
            {
                return false;
            }
        }

        // if the OS is Linux and UOS, create a tray icon
        public void CreateTrayIcon()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux) && IsUos())
            {
                Thread gtkThread = new Thread(new ThreadStart(() =>
                {
                    Gtk.Application.Init();
                    var appBasePath = AppDomain.CurrentDomain.BaseDirectory;
                    var png = Path.Combine(appBasePath, "logo.png");
                    var trayIcon = new StatusIcon(new Pixbuf(png));
                    trayIcon.Visible = true;
                    trayIcon.Activate += (sender, e) => { Console.WriteLine("Tray Icon Clicked"); };

                    var menu = new Menu();
                    var exitItem = new MenuItem("退出");
                    var openItem = new MenuItem("打开");
                    exitItem.Activated += (sender, e) =>
                    {
                        Environment.Exit(0);
                        Gtk.Application.Quit();
                    };
                    openItem.Activated += (sender, e) =>
                    {
                        if (Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
                        {
                            Dispatcher.UIThread.Post(() =>
                            {
                                desktop.MainWindow.IsVisible = true;
                                desktop.MainWindow.WindowState = WindowState.Normal;
                            });
                        }
                    };
                    menu.Append(openItem);
                    menu.Append(exitItem);
                    menu.ShowAll();

                    trayIcon.PopupMenu += (o, args) => { menu.Popup(); };
                    Gtk.Application.Run();
                }));
                gtkThread.Start();
            }
        }

        private void CurrentDomainOnProcessExit(object? sender, EventArgs e)
        {
            try
            {
                _robotContext.StopCurrentMode();
                IBusinessRequestService requestService = _container.Resolve<IBusinessRequestService>();

                var appConfigManager = _container.Resolve<IAppConfigManager>();
                requestService.WorkStateAsync(appConfigManager.RobotConfig.ServerUrl, 2).Wait();
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
            }
        }
    }
}