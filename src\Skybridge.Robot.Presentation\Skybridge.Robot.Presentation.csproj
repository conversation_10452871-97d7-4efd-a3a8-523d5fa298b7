﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
  </PropertyGroup>
  <ItemGroup>
    <AvaloniaXaml Remove="View\**" />
    <Compile Remove="View\**" />
    <EmbeddedResource Remove="View\**" />
    <None Remove="View\**" />
  </ItemGroup>
  <ItemGroup>
    <AvaloniaResource Include="Assets\logo.png" />
    <AvaloniaResource Include="Assets\trayicon.ico" />
    <AvaloniaResource Include="Assets\trayicon.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.3.0" />
    <PackageReference Include="Avalonia.Desktop" Version="11.3.0" />
    <PackageReference Include="Avalonia.ReactiveUI" Version="11.3.0" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.0" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.0" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.3.0" />
    <PackageReference Include="FluentAvaloniaUI" Version="2.3.0" />
    <PackageReference Include="GtkSharp" Version="3.24.24.95" />
    <PackageReference Include="Material.Icons.Avalonia" Version="2.4.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\CommonCore\src\Skybridge.Controls\Skybridge.Controls.csproj" />
    <ProjectReference Include="..\..\..\CommonCore\src\Skybridge.Shared\Skybridge.Shared.csproj" />
    <ProjectReference Include="..\..\..\CommonCore\src\Skybridge.Theme\Skybridge.Theme.csproj" />
    <ProjectReference Include="..\Skybridge.Domain.Core\Skybridge.Domain.Core.csproj" />
    <ProjectReference Include="..\Skybridge.Robot.Application\Skybridge.Robot.Application.csproj" />
    <ProjectReference Include="..\Skybridge.Robot.CommonView\Skybridge.Robot.CommonView.csproj" />
    <ProjectReference Include="..\Skybridge.Robot.Infrastructure\Skybridge.Robot.Infrastructure.csproj" />
    <ProjectReference Include="..\Skybridge.Robot.Styles\Skybridge.Robot.Styles.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
  </ItemGroup>
</Project>
