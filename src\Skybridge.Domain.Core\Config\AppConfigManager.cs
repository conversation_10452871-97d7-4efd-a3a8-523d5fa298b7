using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Reflection.PortableExecutable;
using System.Text;
using System.Threading.Tasks;
using Avalonia.Input;
using CliWrap;
using Newtonsoft.Json;
using Org.BouncyCastle.Bcpg.OpenPgp;
using Skybridge.Domain.Core.Enums;
using Skybridge.Robot.Utils;
using Skybrige.Robot.License;

namespace Skybridge.Domain.Core.Config;

public enum Mode
{
    RemoteServer,
    Local
}

public enum ProductType
{
    [Description("单机版")] SinglePlayer,
    [Description("企业版")] Enterprise
}

public enum VersionType
{
    [Description("设计器")] Designer,
    [Description("执行器")] Actuator
}

public class RobotConfig
{
    public string RobotId { get; set; }
    public string MachineCode { get; set; }
    public string RobotName { get; set; }
    public string ServerUrl { get; set; }
    public Mode Mode { get; set; }
    public ProductType ProductType { get; set; }
    public VersionType VersionType { get; set; }

    /// <summary>
    /// 默认为轮询模式
    /// </summary>
    public RobotTaskMode RobotTaskMode { get; set; } = RobotTaskMode.PollingMode;

    public DateTime ExpiredDate { get; set; }
    public int ListenPort => 10000;
    public int MaxExectorCount { get; set; } = 1;
}

public class ApplicaitonConfig
{
    public string Name { get; set; }
    public string Version { get; set; }
    public string productVersion { get; set; }
    public DateTime PublishTime { get; set; }
    public string Description { get; set; }
}

public class AppConfigManager : IAppConfigManager
{
    public Action<string> OnChangeServerUrl { get; set; }

    public Action<string?> OnChangeTitle { get; set; }
    public Action<string> OnChangeRobotName { get; set; }

    public Action OnChangeTaskMode { get; set; }

    private readonly IFileStorageConfig _fileStorageConfig;

    public RobotConfig? RobotConfig { get; private set; }
    public ApplicaitonConfig ApplicationConfig { get; private set; }
    public Action<Mode> OnChangeMode { get; set; }

    public AppConfigManager(IFileStorageConfig fileStorageConfig)
    {
        _fileStorageConfig = fileStorageConfig;
        Read();
        ReadApplicationConfig();
    }

    private void ReadApplicationConfig()
    {
        var json = File.ReadAllText(Path.Combine(_fileStorageConfig.ApplicationPath, "appsettings.json"));
        ApplicationConfig = JsonConvert.DeserializeObject<ApplicaitonConfig>(json);
    }

    private void Read()
    {
        if (!File.Exists(Path.Combine(_fileStorageConfig.BasePath, "appsettings.json")))
        {
            string machineCode = string.Empty;
            Task.Run(async () =>
            {
                machineCode = await CommonTool.GenerateMachineCode();
            }).Wait();
            string robotId = GetRobotId(machineCode);
            RobotConfig = new RobotConfig
            {
                RobotId = robotId,
                MachineCode = machineCode,
                Mode = Mode.Local,
                ServerUrl = "",
                ProductType = ProductType.Enterprise,
                VersionType = VersionType.Actuator,
                RobotName = "信创执行器",
                MaxExectorCount = 1
            };
            Save("", RobotConfig.RobotName, false, RobotTaskMode.PollingMode);
        }
        else
        {
            try
            {
                var json = File.ReadAllText(Path.Combine(_fileStorageConfig.BasePath, "appsettings.json"));
                RobotConfig = JsonConvert.DeserializeObject<RobotConfig>(json);
                if (RobotConfig == null)
                {
                    throw new Exception("配置文件内容为空或格式不正确");
                }
                string machineCode = RobotConfig.MachineCode;
                if (string.IsNullOrEmpty(machineCode))
                {
                    Task.Run(async () =>
                    {
                        machineCode = await CommonTool.GenerateMachineCode();
                    }).Wait();
                }
                string robotId = GetRobotId(machineCode);
                RobotConfig.RobotId = robotId;
                RobotConfig.MachineCode = machineCode;
            }
            catch (Exception ex)
            {
                throw new Exception("读取配置文件失败，请检查配置文件格式是否正确", ex);
            }

        }
    }

    private string GetRobotId(string machineCode)
    {
        return "R_" + new EncryptioAlgorithm().MD5Encrypt(machineCode).ToUpper();
    }

    public void Save(string serverUrl, string robotName, bool isServerMode, RobotTaskMode robotTaskMode)
    {
        var mode = isServerMode ? Mode.RemoteServer : Mode.Local;

        if (!mode.Equals(RobotConfig.Mode))
        {
            RobotConfig.Mode = mode;
            OnChangeMode?.Invoke(RobotConfig.Mode);
        }

        if (!serverUrl.Equals(RobotConfig.ServerUrl))
        {
            RobotConfig.ServerUrl = serverUrl;
            OnChangeServerUrl?.Invoke(RobotConfig.ServerUrl);
        }
        
        if (!robotName.Equals(RobotConfig.RobotName))
        {
            RobotConfig.RobotName = robotName;
            OnChangeRobotName?.Invoke(RobotConfig.RobotName);
        }

        if (robotTaskMode != RobotConfig.RobotTaskMode)
        {
            RobotConfig.RobotTaskMode = robotTaskMode;
            OnChangeTaskMode?.Invoke();
        }

        File.WriteAllText(Path.Combine(_fileStorageConfig.BasePath, "appsettings.json"),
            JsonConvert.SerializeObject(RobotConfig));
    }
}