<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    <ItemGroup>
      <PackageReference Include="Avalonia" Version="11.3.0" />
    </ItemGroup>
    <ItemGroup>
      <Compile Update="LicenseView.axaml.cs">
        <DependentUpon>LicenseView.axaml</DependentUpon>
        <SubType>Code</SubType>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Skybridge.Domain.Core\Skybridge.Domain.Core.csproj" />
      <ProjectReference Include="..\Skybridge.Robot.License\Skybridge.Robot.License.csproj" />
      <ProjectReference Include="..\Skybridge.Robot.Styles\Skybridge.Robot.Styles.csproj" />
    </ItemGroup>

    <ItemGroup>
      <AvaloniaResource Include="Assets\logo.png" />
      <AvaloniaResource Include="Assets\trayicon.png" />
    </ItemGroup>

</Project>
