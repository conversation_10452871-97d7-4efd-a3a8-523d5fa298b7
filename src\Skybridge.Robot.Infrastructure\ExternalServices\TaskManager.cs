﻿using System.Net;
using Newtonsoft.Json;
using Skybridge.Activities.Private;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Enums;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.DTOs.Task.Requests;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Utils;

namespace Skybridge.Robot.Infrastructure.ExternalServices;

/// <summary>
/// 任务管理器实现，负责管理机器人任务的创建、更新和查询
/// </summary>
public sealed class TaskManager : ITaskManager
{
    private readonly RunningContext _runningContext;
    private readonly ILogService _logService;
    private readonly IBusinessRequestService _businessRequestService;
    private readonly RobotConfig _robotConfig;
    private readonly ILostDataRepository _lostDataRepository;
    private readonly IAppConfigManager _appConfigManager;

    public TaskManager(ILogService logService, IBusinessRequestService businessRequestService,
        RunningContext runningContext, IAppConfigManager appConfigManager, ILostDataRepository lostDataRepository)
    {
        _lostDataRepository = lostDataRepository;
        _runningContext = runningContext;
        _robotConfig = appConfigManager.RobotConfig;
        _logService = logService ?? throw new ArgumentNullException(nameof(logService));
        _businessRequestService = businessRequestService;
        _appConfigManager = appConfigManager;
        runningContext.OnTaskCompleted += OnTaskCompleted;
    }

    private async void OnTaskCompleted(RobotTask robotTask)
    {
        
        if (robotTask.TaskStopInfo != null && robotTask.TaskStopInfo.TaskStopType != TaskStopType.Normal )
        {
            if (robotTask.IsServerTask())
            {
                await UploadFailedLog(robotTask);
            }
        }
    }

    private async Task UploadFailedLog(RobotTask robotTask)
    {
        var tasklogRequest = new TaskLogRequest()
        {
            Component_Name = "",
            Details = robotTask.TaskStopInfo.StopMessage,
            State = 2,
            Task_Id = robotTask.Id,
            SignGuid = Guid.NewGuid().ToString(),
            UploadTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff")
        };

        bool bRes = await _businessRequestService.TaskLogAsync(_robotConfig.ServerUrl, tasklogRequest);
        if (!bRes)
        {
            LostData lostData = new LostData()
            {
                Name = "任务日志",
                Type = LogType.Task,
                Content = JsonConvert.SerializeObject(tasklogRequest),
                TaskId = tasklogRequest.Task_Id,
                SignGuid = tasklogRequest.SignGuid
            };
            await _lostDataRepository.AddAsync(lostData);
        }
    }

    /// <summary>
    /// 加载任务列表
    /// </summary>
    /// <returns>未完成的任务列表</returns>
    public IEnumerable<RobotTask> LoadList()
    {
        try
        {
            return _runningContext.RobotTasks.Values
                .Where(x => x.Status == 0)
                .OrderByDescending(x => x.StartTime);
        }
        catch (Exception ex)
        {
            _logService.LogError( "Failed to load task list",ex);
            return Enumerable.Empty<RobotTask>();
        }
    }

    /// <summary>
    /// 添加新任务
    /// </summary>
    /// <param name="port">监听端口</param>
    /// <param name="taskId">任务ID</param>
    /// <param name="projectId">项目ID</param>
    /// <param name="baseUrl">基础URL</param>
    /// <param name="serverTaskId">服务器任务ID</param>
    /// <param name="inParameters">输入参数</param>
    /// <param name="isLogDebug">是否记录组件执行步骤</param>
    /// <returns>Base64编码的任务信息</returns>
    public string GenerateBase64(int port, string taskId,string serverTaskId, string projectId, string baseUrl,string token, 
        IDictionary<string, object> inParameters,bool isLogDebug)
    {
        ArgumentNullException.ThrowIfNull(taskId);
        ArgumentNullException.ThrowIfNull(projectId);
        ArgumentNullException.ThrowIfNull(baseUrl);
        ArgumentNullException.ThrowIfNull(inParameters);

        try
        {
            var proj = _runningContext.RobotProjects.Find(x => x.Id.Equals(projectId));
            if (proj == null)
            {
                throw new InvalidOperationException($"Project not found: {projectId}");
            }

            var taskInfo = Generate(port, taskId, serverTaskId,proj, token,inParameters,isLogDebug);
            return JsonConvert.SerializeObject(taskInfo).ToBase64();
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to add task: {taskId}", ex);
            throw;
        }
    }

    private TaskInfo Generate(int port, string taskId,string serverTaskId, RobotProject project,string token, 
        IDictionary<string, object> inParameters,bool isLogDebug)
    {
        return new()
        {
            ActivityRoot = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "packages"),
            Parameters = JsonConvert.SerializeObject(inParameters),
            ProjectCode = project.Code,
            ProjectId = project.Id,
            ProjectName = project.Name,
            ProjectRoot = project.FilePath,
            ProjectVersion = project.Version,
            RobotId = _appConfigManager.RobotConfig.RobotId,
            ActivityParameter = new()
            {
                RobotExecuteContext = new()
                {
                    DateTokenId = token,
                    FlowId = "",
                    SceneId = "",
                    ServiceId = serverTaskId,
                    TaskId = taskId,
                    LogLevel = isLogDebug ? "Debug":"Info",
                },
                ServerInfoContext = new()
                {
                    Gateway = "/api",
                    ServerEndPoint = _robotConfig.ServerUrl,
                    ServerRange = ""
                }
            },
            StartupMain = "Main.xaml",
            TaskId = taskId,
            TaskName = project.Name,
            ServerUrl = $"http://{IPAddress.Loopback}:{port}",
        };
    }
}