﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia.Controls;
using Skybridge.Domain.Core;
using Skybridge.Domain.Core.Config;
using Skybridge.Robot.Desktop.Views;

namespace Skybridge.Robot.Presentation.ViewModels.AboutPage
{
    public class AboutPageImpl : IPage
    {
        public string Name => "关于";

        public UserControl View { get; }

        public int Order => 4;

        public string Icon => "rti-about";

        private AboutViewModel _aboutViewModel;
        private IAppConfigManager _appConfigManager;

        public AboutPageImpl(IAppConfigManager appConfigManager, LicenseView licenseView)
        {
            _appConfigManager = appConfigManager;
            _aboutViewModel = new AboutViewModel()
            {
                RobotId = $"机器人编号：{_appConfigManager.RobotConfig.RobotId}",
                Version = _appConfigManager.RobotConfig.ProductType.Equals(ProductType.Enterprise) ? "温馨提示: 执行器 - 企业版" : "温馨提示: 执行器 - 单机版",
                ExpiredDate = $"过期时间：{_appConfigManager.RobotConfig.ExpiredDate.ToString("yyyy-MM-dd")}  已激活",
                PublishTime = $"发布时间：{_appConfigManager.ApplicationConfig.PublishTime.ToString("yyyy-MM-dd")}",
            };
            View = new AboutView(_appConfigManager, licenseView)
            {
                DataContext = _aboutViewModel
            };
        }

        public void Load()
        {
            _aboutViewModel.RobotId = $"机器人编号：{_appConfigManager.RobotConfig.RobotId}";
            _aboutViewModel.Version = _appConfigManager.RobotConfig.ProductType.Equals(ProductType.Enterprise) ? "温馨提示: 执行器 - 企业版" : "温馨提示: 执行器 - 单机版";
            _aboutViewModel.ExpiredDate = $"过期时间：{_appConfigManager.RobotConfig.ExpiredDate.ToString("yyyy-MM-dd")}  已激活";
            _aboutViewModel.PublishTime = $"发布时间：{_appConfigManager.ApplicationConfig.PublishTime.ToString("yyyy-MM-dd")}";
        }
    }
}