﻿using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Platform.Storage;
using Avalonia.Threading;
using ReactiveUI;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Exector;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Presentation.Services;
using Skybridge.Robot.Styles.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using Autofac;
using Avalonia.Vulkan;
using FluentAvalonia.UI.Controls;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Presentation.ViewModels.RobotSetting;

namespace Skybridge.Robot.Presentation.ViewModels
{
    public class MainWindowViewModel : ViewModelBase
    {
        private ObservableCollection<Router> _routers = new ObservableCollection<Router>();

        public ObservableCollection<Router> Routers
        {
            get => _routers;
            set => this.RaiseAndSetIfChanged(ref _routers, value);
        }

        private Router _selectedRouter = new Router();

        public Router SelectedRouter
        {
            get => _selectedRouter;
            set => this.RaiseAndSetIfChanged(ref _selectedRouter, value);
        }

        private int _runningCount;

        public int RunningCount
        {
            get => _runningCount;
            set => this.RaiseAndSetIfChanged(ref _runningCount, value);
        }

        private bool _isServerMode;

        public bool IsServerMode
        {
            get => _isServerMode;
            set => this.RaiseAndSetIfChanged(ref _isServerMode, value);
        }

        private readonly IAppConfigManager? _appConfigManager;
        private readonly IProjectManager? _projectManager;
        private readonly IExecManager? _execManager;
        private readonly PageService? _pageService;
        private readonly DialogService _dialogService;
        private readonly RobotContext _robotContext;
        private readonly ILifetimeScope _lifetimeScope;


        public MainWindowViewModel(DialogService dialogService, PageService pageSerivce,
            IProjectManager projectManager, IAppConfigManager appConfigManager, IExecManager execManager,
             RunningContext runningContext,RobotContext robotContext,ILifetimeScope lifetimeScope)
        {
            _lifetimeScope = lifetimeScope;
            _robotContext = robotContext;
            _dialogService = dialogService;
            _pageService = pageSerivce;
            _projectManager = projectManager;
            _appConfigManager = appConfigManager;
            _execManager = execManager;
            runningContext.OnTaskCountChanged += ChangeRunningCount;
            LoadData();
        }

        private void LoadData()
        {
            _isServerMode = !_appConfigManager?.RobotConfig?.Mode.Equals(Mode.RemoteServer) ?? false;
            _appConfigManager.OnChangeMode += ChangeMode;
            Routers = _pageService?.GetPages() ?? new ObservableCollection<Router>();
            SelectedRouter = Routers.FirstOrDefault() ?? new Router();
        }

        public ReactiveCommand<Router, Unit> OnRouterSelectedCommand => ReactiveCommand.Create<Router>(OnRouterSelected);
        public ReactiveCommand<Unit, Unit> ImportProjectCommand => ReactiveCommand.Create(ImportProject);

        public ReactiveCommand<Unit, Unit> ShowRobotSettingCommand =>
            ReactiveCommand.CreateFromTask(ShowRobotSetting);

        private async Task ShowRobotSetting()
        {
            var robotSettingViewModel = _lifetimeScope.Resolve<RobotSettingViewModel>();
            var confirmBtn = new TaskDialogButton("确认", 1);
            var cancelBtn = new TaskDialogButton("取消", 0);
            var view = new RobotSettingView();
            var td = new TaskDialog
            {
                Title = "机器人设置",
                ShowProgressBar = false,
                Content = view,
                Buttons =
                {
                    confirmBtn,
                    cancelBtn
                },
                DataContext = robotSettingViewModel
            };
            confirmBtn.Command = robotSettingViewModel.ConfirmCommand;
            robotSettingViewModel.SetView(view);
            robotSettingViewModel.CreateConfirmTask();
            td.Closing += async (sender, args) =>
            {
                var defferal = args.GetDeferral();
                if (args.Result.Equals(1))
                {
                    bool confirmRes = await robotSettingViewModel.ConfirmTask.Task;
                    args.Cancel = !confirmRes;
                    robotSettingViewModel.CreateConfirmTask();
                }
                defferal.Complete();
            };
            var desktop = Avalonia.Application.Current.ApplicationLifetime as IClassicDesktopStyleApplicationLifetime;
            td.XamlRoot = desktop.MainWindow;
            var tdResult = await td.ShowAsync();
        }

        private void ChangeRunningCount(int count) => Dispatcher.UIThread.Invoke(() => RunningCount = count);

        private void ChangeMode(Mode mode) => Dispatcher.UIThread.Invoke(() => IsServerMode = mode != Mode.RemoteServer);

        private void OnRouterSelected(Router router)
        {
            SelectedRouter = router;
            _pageService.Load(router.Name);
        }

        private async void ImportProject()
        {
            var result = await StorageService.GetStorageProvider()
                ?.OpenFilePickerAsync(new()
                {
                    Title = "请选择项目zip文件",
                    FileTypeFilter = new List<FilePickerFileType>
                    {
                       StorageService.Zip
                    },
                    AllowMultiple = false
                })!;
            var path = result.FirstOrDefault()?.TryGetLocalPath();
            if (path == null)
                return;
            var desktop = Avalonia.Application.Current.ApplicationLifetime as IClassicDesktopStyleApplicationLifetime;
            if (!path.EndsWith(".zip"))
            {
                MessageBox.Show(desktop.MainWindow, "文件格式不正确!", TimeSpan.FromSeconds(3));
                return;
            }
            try
            {
                var importResult = await _robotContext.LocalImport(path, false, string.Empty, string.Empty);
                MessageBox.Show(desktop.MainWindow, importResult.Item2, TimeSpan.FromSeconds(3));
            }
            catch (Exception e)
            {
                MessageBox.Show(desktop.MainWindow, "当前项目文件存在异常,无法解压!", TimeSpan.FromSeconds(3));
            }
        }
    }
}