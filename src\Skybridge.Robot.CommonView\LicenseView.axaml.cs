﻿using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Interactivity;
using Avalonia.Platform.Storage;
using Skybridge.Domain.Core;
using Skybridge.Domain.Core.Config;
using Skybridge.Robot.Styles.Components;
using Skybridge.Robot.Styles.Services;
using Skybrige.Robot.License;

namespace Skybridge.Robot.Desktop.Views;

public partial class LicenseView : RobotWindow
{
    private readonly RobotLicense _licenseManager;
    private readonly IAppConfigManager _appConfigManager;
    private readonly IPluginManager _pluginManager;

    public Window Parent { get; set; }

    public LicenseView(RobotLicense licenseManager, LicenseViewModel _licenseViewModel,
        IAppConfigManager appConfigManager, IPluginManager pluginManager)
    {
        _licenseManager = licenseManager;
        _appConfigManager = appConfigManager;
        _pluginManager = pluginManager;
        DataContext = _licenseViewModel;
        InitializeComponent();
    }

    private async void ImportLicense(object? sender, RoutedEventArgs e)
    {
        var result = await StorageService.GetStorageProvider()
            ?.OpenFilePickerAsync(new() {
                Title = "请选择license文件",
                FileTypeFilter = new List<FilePickerFileType>
                {
                    StorageService.License
                },
                AllowMultiple = false
            })!;
        if (!result.Any())
            return;
        await using var stream = await result[0].OpenReadAsync();
        using var license = new StreamReader(stream);
        var licenseContent = await license.ReadToEndAsync();
        var res = await _licenseManager.ImportLicense(licenseContent, (int)_appConfigManager.RobotConfig.ProductType,
            (int)_appConfigManager.RobotConfig.VersionType);
        if (res.Item1)
        {
            if (!res.Item2.Equals(string.Empty))
                MessageBox.ShowDialog(this, res.Item2);
            else
            {
                if (Application.Current.ApplicationLifetime is not IClassicDesktopStyleApplicationLifetime desktop)
                    return;
                desktop.MainWindow = Parent;
                Parent.Show();
                Hide();
            }
        }
        else
            MessageBox.ShowDialog(this, "License是无效或机器码不匹配");
    }

    private void Cancel(object? sender, RoutedEventArgs e)
    {
        Close();
    }

    protected override void OnClosing(WindowClosingEventArgs e)
    {
        if (Avalonia.Application.Current.ApplicationLifetime is not IClassicDesktopStyleApplicationLifetime desktop)
            return;
        if (!desktop.MainWindow.Equals(this))
        {
            e.Cancel = true;
            Hide();
        }
    }
}