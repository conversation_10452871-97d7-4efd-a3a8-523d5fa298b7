using System.Collections.Concurrent;
using System.IO.Compression;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Enums;
using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Model;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.DTOs.Robot.Requests;
using Skybridge.Robot.Application.DTOs.Task.Requests;
using Skybridge.Robot.Application.DTOs.Task.Responses;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Utilities;
using Skybridge.Robot.Infrastructure.WebSocket.Models;
using Skybridge.Robot.Utils;

namespace Skybridge.Robot.Infrastructure.Contexts;

/// <summary>
/// 机器人上下文，管理机器人的生命周期、状态和通信
/// </summary>
public class RobotContext : IDisposable
{
    private readonly IExecManager _execManager;
    private readonly IBusinessRequestService _businessRequestService;
    private readonly IFileStorageConfig _fileStorageConfig;
    private readonly int LoopTime = 10;
    private readonly IProjectManager _projectManager;
    private readonly RunningContext _runningContext;
    private readonly ILogService _logService;
    private readonly IWebSocketClient _webSocketClient;
    private CancellationTokenSource _monitorCts;
    private readonly EnvironmentConfig _environmentConfig;
    private bool _isWebSocketMode;
    private CancellationTokenSource? _webSocketReconnectCts;
    private Task? _webSocketReconnectTask;
    private readonly ConcurrentDictionary<string, Task<bool>> _activeDownloads = new ConcurrentDictionary<string, Task<bool>>();
    private CancellationTokenSource? _pollingCancellationTokenSource;

    private bool _isBusy;
    private readonly IAppConfigManager _appConfigManager;
    private readonly RobotConfig _robotConfig;

    public bool IsBusy
    {
        get { return _isBusy; }
        set
        {
            if (_isBusy != value)
            {
                _isBusy = value;
                OnBusyChanged?.Invoke(_isBusy);
            }
        }
    }

    #region 机器人属性
    public bool IsRemote => _appConfigManager.RobotConfig.Mode == Mode.RemoteServer;
    public string Id => _appConfigManager.RobotConfig.RobotId;

    public string BaseUrl => _appConfigManager.RobotConfig.ServerUrl;
    #endregion
    public event Action<bool> OnServerStateChanged;

    private bool _isServerAvalible;
    private CancellationTokenSource _reportStateCts;
    private readonly JsonDbContext _jsonDbContext;
    private readonly ILostDataRepository _lostDataRepository;

    public bool IsServerAvalible
    {
        get
        {
            return _isServerAvalible;
        }
        set
        {
            if (_isServerAvalible != value)
            {
                _isServerAvalible = value;
                OnServerStateChanged?.Invoke(_isServerAvalible);
            }
        }
    }

    public Action<bool>? OnBusyChanged { get; set; }

    public RobotContext(
        IExecManager execManager,
        IBusinessRequestService businessRequestService,
        IFileStorageConfig fileStorageConfig,
        IProjectManager projectManager,
        RunningContext runningContext,
        IAppConfigManager appConfigManager,
        ILogService logService,
        IWebSocketClient webSocketClient,
        EnvironmentConfig environmentConfig,
        JsonDbContext jsonDbContext,
        ILostDataRepository lostDataRepository)
    {
        
        _execManager = execManager;
        _logService = logService;
        _fileStorageConfig = fileStorageConfig;
        _projectManager = projectManager;
        _runningContext = runningContext;
        _businessRequestService = businessRequestService;
        _webSocketClient = webSocketClient;
        _environmentConfig = environmentConfig;
        _appConfigManager = appConfigManager;
        _robotConfig = appConfigManager.RobotConfig;
        _jsonDbContext = jsonDbContext;
        _lostDataRepository = lostDataRepository;
        // 注册事件处理程序
        _ = RegisterEventHandlers();
        OnBusyChanged += OnBusyChangedHandler;
        runningContext.OnTaskLogEndUpdated += async void (task, status) =>
        {
            try
            {
                if(task == null)
                {
                    return;
                }
                await Task.Delay(5000);
                await _execManager.StopExecutorAsync(task.Id, new TaskStopInfo("任务结束", TaskStopType.Normal));
            }
            catch (Exception e)
            {
                _logService.LogError("LogEnd任务结束处理失败",e);
            }
        };
    }

    private async void OnRobotModeChanged(Mode mode)
    {
        StopCurrentMode(); 
        if (mode == Mode.Local) 
        {
            //关闭所有连接 停止心跳等
            StopServerStateMonitorTask();
        }
        else
        {
            await _businessRequestService.WorkStateAsync(_robotConfig.ServerUrl, IsBusy ? 1 : 0);
            StartServerStateMonitorTask();
            if (_robotConfig.RobotTaskMode == RobotTaskMode.PollingMode)
            {
                StartPollingTask();
            }
            else
            {
                await InitializeWebsocketMode();
            }
        }
    }

    private async void OnBusyChangedHandler(bool isBusy)
    {
        await _businessRequestService.WorkStateAsync(_robotConfig.ServerUrl, isBusy ? 1 : 0);
    }

    private async Task RegisterEventHandlers()
    {
        _appConfigManager.OnChangeMode += OnRobotModeChanged;
        _runningContext.TaskQueueEmpty += () =>
        {
            IsBusy = false;
        };
        _runningContext.TaskQueueNotEmpty += () =>
        {
            IsBusy = true;
        };
        // WebSocket事件处理
        _webSocketClient.OnMessageReceived += HandleWebSocketMessage;
        _webSocketClient.OnConnected += async (status) =>
        {
            _logService.LogInfo($"WebSocket connected: { status.Message}");
            await RegisterRobot();
        };

        _appConfigManager.OnChangeTaskMode += async () =>
        {
            if (_robotConfig.Mode == Mode.RemoteServer)
            {
                await SetServerUrlAndRegister();
                await InitializeTaskMode();
            }
            else
            {
                StopCurrentMode();
            }
        };
        _appConfigManager.OnChangeServerUrl += async void (url) =>
        {
            try
            {
                StopCurrentMode();
                if (_robotConfig.Mode == Mode.RemoteServer)
                {
                    await SetServerUrlAndRegister();
                    await InitializeTaskMode();
                }
            }
            catch (Exception e)
            {
                _logService.LogError("服务器地址更改失败", e);
            }
        };
        if (_robotConfig.Mode == Mode.RemoteServer)
        {
            await SetServerUrlAndRegister();
            try
            {
                await InitializeTaskMode();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }

        // 初始化
        //开始心跳线程监测
        StartServerStateMonitorTask();
        StartRobotStateReportTask();
    }

    private void StartRobotStateReportTask()
    {
        _reportStateCts?.Cancel();
        _reportStateCts = new CancellationTokenSource();
        Task.Factory.StartNew(async (_) =>
        {
            while (!_reportStateCts.IsCancellationRequested)
            {
                if (_robotConfig.Mode == Mode.Local || !IsServerAvalible)
                {
                    await Task.Delay(20 * 1000);
                    continue;
                }
                await _businessRequestService.WorkStateAsync(_robotConfig.ServerUrl, IsBusy ? 1 : 0);
                await Task.Delay(20 * 1000);
            }
        }, TaskCreationOptions.LongRunning, _reportStateCts.Token);
    }

    private async void HandleWebSocketMessage(string message)
    {
        try
        {
            if (message.StartsWith("该消息不支持处理"))
            {
                return;
            }
            var msg = Message.Create(message);
            if (msg == null || msg.Value == null)
            {
                return;
            }
            switch (msg.MsType)
            {
                case MessageType.task:
                    var taskInfo = JsonConvert.DeserializeObject<GetTaskResponse>(msg.Value.ToString());
                    if (taskInfo == null)
                    {
                        throw new ArgumentException("Invalid task info");
                    }
                    await RunTask(taskInfo);
                    break;

                case MessageType.stop:
                    string taskId = msg.Value.ToString();
                    //停止任务逻辑
                    await _execManager.StopExecutorAsync(taskId, new TaskStopInfo("远程停止任务", TaskStopType.RemoteStop));
                    break;

                case MessageType.license:
                    break;

                case MessageType.restart:
                    break;
                
                case MessageType.health:
                    // 心跳消息，用于给服务器确认连接状态
                    var res = Message.Create(MessageType.health, _robotConfig.RobotId);
                    await _webSocketClient.SendMessageAsync(res.ToString());
                    break;
            }
        }
        catch (Exception ex)
        {
            _logService.LogError( "Error processing WebSocket message",ex);
        }
    }

    /// <summary>
    /// Starts the WebSocket reconnection loop.
    /// </summary>
    public void StartReconnectWebSocket()
    {
        // Prevent multiple concurrent reconnect tasks
        if (_webSocketReconnectTask != null && !_webSocketReconnectTask.IsCompleted)
            return;

        _webSocketReconnectCts = new CancellationTokenSource();
        _webSocketReconnectTask = Task.Run(
            () => ReconnectWebSocketLoop(_webSocketReconnectCts.Token));
    }

    /// <summary>
    /// Stops the WebSocket reconnection loop.
    /// </summary>
    public void StopReconnectWebSocket()
    {
        _webSocketReconnectCts?.Cancel();
        _webSocketReconnectTask = null;
    }

    /// <summary>
    /// The actual reconnect loop, runs in background.
    /// </summary>
    private async Task ReconnectWebSocketLoop(CancellationToken token)
    {
        while (_isWebSocketMode && !_webSocketClient.IsConnected && !token.IsCancellationRequested)
        {
            try
            {
                var wsUrl = AdaptWebSocketUrl(_robotConfig.ServerUrl);
                await _webSocketClient.ConnectAsync(wsUrl);
                await Task.Delay(5000, token); // Reconnect interval
            }
            catch (OperationCanceledException)
            {
                // Task was cancelled, exit loop
                break;
            }
            catch (Exception ex)
            {
                _logService.LogError( "WebSocket reconnection failed",ex);
                try
                {
                    await Task.Delay(5000, token); // Wait before retrying
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }
        }
    }

    private async Task ReconnectWebSocket()
    {
        while (_isWebSocketMode && !_webSocketClient.IsConnected)
        {
            try
            {
                var wsUrl = AdaptWebSocketUrl(_robotConfig.ServerUrl);
                await _webSocketClient.ConnectAsync(wsUrl);
                await Task.Delay(5000); // 重连间隔
            }
            catch (Exception ex)
            {
                _logService.LogError( "WebSocket reconnection failed",ex);
                await Task.Delay(5000); // 重连失败后等待
            }
        }
    }

    private string AdaptWebSocketUrl(string httpUrl)
    {
        var uri = new Uri(httpUrl);
        var wsScheme = uri.Scheme == "https" ? "wss" : "ws";
        return $"{wsScheme}://{uri.Authority}/ws/robot/{_robotConfig.RobotId}";
    }

    private async Task InitializeTaskMode()
    {
        // 停止当前模式
        StopCurrentMode();

        // 初始化新模式
        _isWebSocketMode = _robotConfig.RobotTaskMode == RobotTaskMode.WebsocketMode;

        if (_isWebSocketMode)
        {
            await InitializeWebsocketMode();
            StartReconnectWebSocket();
        }
        else
        {
            InitializePollingMode();
        }

        await ChangeState(IsBusy ? 1 : 0);
    }

    public void StopCurrentMode()
    {
        try
        {
            StopReconnectWebSocket();
            _pollingCancellationTokenSource?.Cancel();
            _pollingCancellationTokenSource?.Dispose();
            _pollingCancellationTokenSource = null;
            if (_webSocketClient.IsConnected)
            {
                _webSocketClient.CloseAsync().Wait();
            }

            _isWebSocketMode = false;
        }
        catch (Exception ex)
        {
            _logService.LogError("Error stopping current mode",ex);
        }
    }

    public async Task InitializeWebsocketMode()
    {
        try
        {
            _isWebSocketMode = true;
            var serverUrl = _robotConfig.ServerUrl;

            _logService.LogInfo($"Initializing WebSocket mode with server URL: {serverUrl}");
            // close the client before a new connection create
            await _webSocketClient.CloseAsync();
            // WebSocket client will handle URL normalization internally
            await _webSocketClient.ConnectAsync(serverUrl);
            _logService.LogInfo("WebSocket mode initialized successfully");
        }
        catch (Exception ex)
        {
            _logService.LogError( "Failed to initialize WebSocket mode",ex);
            throw;
        }
    }

    public void InitializePollingMode()
    {
        _pollingCancellationTokenSource = new CancellationTokenSource();
        StartPollingTask();
        _logService.LogInfo("Polling mode initialized");
    }

    public async Task<bool> RegisterRobot()
    {
        try
        {
            await _businessRequestService.RegisterRobotAsync(
                _robotConfig.ServerUrl,
                new RobotRegisterRequest
                {
                    Host = _environmentConfig.IpAddress,
                    Name = _robotConfig.RobotName,
                    Port = 1000,
                    Robot_ID = _robotConfig.RobotId,
                    Type = _robotConfig.RobotTaskMode == RobotTaskMode.PollingMode ? 1 : 2,
                    System = _environmentConfig.OSPlatformName,
                    Memory = _environmentConfig.MemorySize,
                    Cpu = _environmentConfig.CpuInfo,
                    ExpiredDate = _robotConfig.ExpiredDate.ToString("yyyy-MM-dd")
                });
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 更改机器人状态
    /// state: 0:空闲 1:忙碌 2:离线 3:失联
    /// </summary>
    public async Task ChangeState(int state)
    {
        try
        {
            await _businessRequestService.WorkStateAsync(_robotConfig.ServerUrl, state);
            _logService.LogInfo($"机器人状态更改为: {state}");
        }
        catch (Exception e)
        {
            _logService.LogError("机器人状态更改失败",e);
        }
    }

    private async Task<bool> SetServerUrlAndRegister()
    {
        try
        {
            if (await RegisterRobot())
            {
                _logService.LogInfo("服务器连接成功");
                return true;
            }
            else
            {
                _logService.LogError("服务器连接失败");
                return false;
            }
        }
        catch (Exception e)
        {
            _logService.LogError("服务器连接失败，请检查服务器地址",e);
            return false;
        }
    }

    private async Task UploadTaskLog(TaskLog taskLog,RobotTask? task)
    {
        try
        {
            TaskLogRequest taskLogRequest = new()
            {
                Component_Name = "",
                Details = taskLog.traceInfo == null ? 
                    taskLog.Message == null ? 
                        string.Empty:taskLog.Message : taskLog.traceInfo.Message,
                State = GetLogStatus(taskLog.Status),
                Task_Id = taskLog.TaskId,
                UploadTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff"),
            };
            if (task != null)
            {
                task.LogUploadTimer.Stop();
                task.IsReceivedLog = true;
            }
            bool uploadTaskLogRes =  await _businessRequestService.TaskLogAsync(_robotConfig.ServerUrl,taskLogRequest );


            _runningContext.TaskLogUploadDict.TryRemove(taskLog.TaskId, out _);
            if (!uploadTaskLogRes)
            {
                string taskLogContent = JsonConvert.SerializeObject(taskLogRequest);
                LostData lostData = new LostData()
                {
                    Name = "任务日志",
                    Type = LogType.Task,
                    Content = taskLogContent,
                    TaskId = taskLogRequest.Task_Id,
                    SignGuid = taskLogRequest.SignGuid
                };
                await _lostDataRepository.AddAsync(lostData);
            }

            if (!uploadTaskLogRes)
            {
                _logService.LogInfo($"上传任务日志{taskLog.TaskId}失败");
            }
            else
            {
                _logService.LogInfo($"上传任务日志{taskLog.TaskId}成功");
            }
        }
        
        catch (Exception e)
        {
            _logService.LogError("日志上传失败",e);
        }
    }

    private async Task LogProcess(TaskLog taskLog)
    {
        var task = _runningContext.RobotTasks.FirstOrDefault(d => d.Key.Equals(taskLog.TaskId)).Value;
        if (task == null)
        {
            task = _runningContext.TaskLogUploadDict.FirstOrDefault(x => x.Key.Equals(taskLog.TaskId)).Value;
        }
        bool isServerTask = int.TryParse(taskLog.TaskId, out _);
        bool hasTraceInfo = taskLog.traceInfo != null;
        if (hasTraceInfo)
        {
            if (taskLog.traceInfo?.Source != null && taskLog.traceInfo.Source.Equals("WriteLine"))
            {
                return;
            }
            ActivityLogModel activityLogModel = GetActivityLogModel(taskLog);
            if (isServerTask)
            {
                UploadActivityLogTask(activityLogModel);
            }
        }
        else
        {
            if (taskLog.Status.Equals("LogEnd"))
            {
                _runningContext.OnTaskLogEndUpdated(task, taskLog.Status);
                return;
            }

            if (task != null)
            {
                task.IsSuccess = GetStatus(taskLog) == 1;
            }
            if (isServerTask)
            {
                await UploadTaskLog(taskLog,task);
            }
            else
            {
                _runningContext.TaskLogUploadDict.TryRemove(taskLog.TaskId, out _);
            }
        }
    }
    
    private bool IsServerTask(string id)
    {
        return int.TryParse(id, out int _);
    }
    public int GetStatus(TaskLog taskLog)
    {
        if (taskLog.Status == "Running")
        {
            return 0;
        }

        if (taskLog.Status == "Fail")
        {
            return 2;
        }

        return 1;
    }
    #region setActivityLog

    private ActivityLogModel GetActivityLogModel(TaskLog taskLog)
    {
        ActivityLogModel activityLogModel = new ActivityLogModel();
        activityLogModel.ActivityName = GetActivityName(taskLog.traceInfo.Message);
        activityLogModel.TaskDetail = GetActivityState(taskLog.traceInfo.Message);
        DateTime? startTime = GetStartTime(taskLog.traceInfo.Message);
        long milliseconds = 0;
        if (startTime != null)
        {
            milliseconds = (long)(startTime.Value.ToUniversalTime() - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;
        }
        activityLogModel.CreateTime = milliseconds.ToString();
        activityLogModel.TaskId = taskLog.TaskId;
        activityLogModel.ProjectName = taskLog.TaskId;
        activityLogModel.LogType = GetLogType(taskLog.traceInfo.Message);
        return activityLogModel;
    }

    private string GetActivityName(string message)
    {
        if (string.IsNullOrEmpty(message))
        {
            return message;
        }
        string componentName = Regex.Match(message, @"名称：\s+(\S+)").Groups[1].Value;
        return componentName;
    }

    private string GetLogType(string message)
    {
        if (string.IsNullOrEmpty(message))
        {
            return message;
        }
        string state = Regex.Match(message, @"状态：(\S+)").Groups[1].Value;

        if (state.Equals("Executing"))
        {
            return "INFO";
        }
        else if (state.Equals("Closed"))
        {
            return "INFO";
        }
        else if (state.Equals("Faulted"))
        {
            return "ERROR";
        }
        else
        {
            return "INFO";
        }
    }

    private string GetActivityState(string message)
    {
        if (string.IsNullOrEmpty(message))
        {
            return message;
        }
        string state = Regex.Match(message, @"状态：(\S+)").Groups[1].Value;

        if (state.Equals("Executing"))
        {
            return "开始";
        }
        else if (state.Equals("Closed"))
        {
            return "结束";
        }
        else if (state.Equals("Faulted"))
        {
            return "失败";
        }
        else
        {
            return message;
        }
    }

    private DateTime? GetStartTime(string message)
    {
        if (string.IsNullOrEmpty(message))
        {
            return null;
        }
        string time = Regex.Match(message, @"跟踪时间：(\S+)").Groups[1].Value;
        if (!string.IsNullOrEmpty(time))
        {
            return Convert.ToDateTime(time);
        }
        else
        {
            return null;
        }
    }

    private void UploadActivityLogTask(ActivityLogModel activityLogModel)
    {
        Task.Run(async () =>
        {
            string activityLogContent = JsonConvert.SerializeObject(activityLogModel);
            await _businessRequestService.UploadActivityLog(_robotConfig.ServerUrl, activityLogContent);
        });
    }

    #endregion setActivityLog

    private static int GetLogStatus(string status) => status switch
    {
        "Running" => 0,
        "Fail" => 2,
        _ => 1
    };
    private async Task<(bool IsSuccess, string ErrorMessage)> DownLoadProject(string baseUrl, RobotProject robotProject)
    {
        if (Directory.Exists(_fileStorageConfig.TempPath))
        {
            Directory.Delete(_fileStorageConfig.TempPath,true);
        }
        Directory.CreateDirectory(_fileStorageConfig.TempPath);
        var zipPath = Path.Combine(_fileStorageConfig.TempPath, 
            $"{GetProjectDirName(true, robotProject.Name, robotProject.Code, robotProject.Version)}.zip");
        var downloadRes = await _businessRequestService.DownloadFileAsync(baseUrl, robotProject.ProjectContentId, zipPath);
        if (!downloadRes.IsSuccess)
        {
            return (false, $"下载项目文件失败:{downloadRes.ErrorMessage}");
        }
        var importRes = RemoteImport(zipPath, robotProject);
        if (!importRes.IsSuccess)
        {
            _logService.LogError($"导入项目出现错误:{importRes.ErrorMessage}");
        }
        return importRes;
    }

    private void StartServerStateMonitorTask()
    {
        // Cancel any existing monitoring task
        _monitorCts?.Cancel();
        _monitorCts = new CancellationTokenSource();

        Task.Factory.StartNew(async (_) =>
        {
            try
            {
                // Get polling interval from service settings, default to 5 seconds if not configured
                int pollingInterval = 5 * 1000;

                while (!_monitorCts.Token.IsCancellationRequested)
                {
                    try
                    {
                        // Skip checking if robot is not in remote mode
                        if (_robotConfig.Mode != Mode.RemoteServer)
                        {
                            await Task.Delay(pollingInterval, _monitorCts.Token);
                            continue;
                        }

                        if (string.IsNullOrEmpty(_robotConfig.ServerUrl))
                        {
                            IsServerAvalible = false;
                            _logService.LogError("Server URL is not configured");
                            await Task.Delay(pollingInterval, _monitorCts.Token);
                            continue;
                        }

                        bool isServerAvailable = await _businessRequestService.GetServerStateAsync(_robotConfig.ServerUrl);

                        // Only update if state changed to avoid unnecessary events
                        if (IsServerAvalible != isServerAvailable)
                        {
                            IsServerAvalible = isServerAvailable;
                            _logService.LogInfo($"Server state changed to: {(isServerAvailable ? "Available" : "Unavailable")}");
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw; // Re-throw cancellation to exit the loop
                    }
                    catch (Exception ex)
                    {
                        IsServerAvalible = false;
                        _logService.LogError($"Error monitoring server state: {ex.Message}");
                    }

                    await Task.Delay(pollingInterval, _monitorCts.Token);
                }
            }
            catch (OperationCanceledException)
            {
                // Normal cancellation, no need to log
            }
            catch (Exception ex)
            {
                _logService.LogError($"Server monitor task failed: {ex.Message}");
            }
        }, _monitorCts.Token);
    }

    public void StopServerStateMonitorTask()
    {
        _logService.LogInfo("停止执行器心跳监测");
        _monitorCts.Cancel();
    }


    public void StartPollingTask()
    {
        if (_pollingCancellationTokenSource == null)
        {
            _pollingCancellationTokenSource = new CancellationTokenSource();
        }

        Task.Factory.StartNew(async () =>
        {
            while (!_pollingCancellationTokenSource.Token.IsCancellationRequested)
            {
                if (_robotConfig.Mode != Mode.RemoteServer  ||
                    _isWebSocketMode  || IsBusy ||  _runningContext.TaskLogUploadDict.Count > 0) // Don't poll if in WebSocket mode
                {
                    await Task.Delay(LoopTime * 1000, _pollingCancellationTokenSource.Token);
                    continue;
                }

                try
                {
                    Console.WriteLine("开始请求任务...");
                    var baseUrl = _robotConfig.ServerUrl;
                    var taskInfo = await _businessRequestService.GetTaskAsync(baseUrl);

                    if (taskInfo == null)
                    {
                        await Task.Delay(LoopTime * 1000, _pollingCancellationTokenSource.Token);
                        continue;
                    }
                    await RunTask(taskInfo);
                }
                catch (OperationCanceledException)
                {
                    _logService.LogInfo("轮询任务已停止");
                    break;
                }
                catch (Exception e)
                {
                    _logService.LogError("任务处理失败",e);
                    await Task.Delay(LoopTime * 1000, _pollingCancellationTokenSource.Token);
                }
            }
        }, _pollingCancellationTokenSource.Token);
    }

    private async Task RunTask(GetTaskResponse taskInfo)
    {
        RobotTask receivedTask = null;
        string baseUrl = _robotConfig.ServerUrl;
        var proj = _runningContext.RobotProjects.Find(x =>
            x.Name.Equals(taskInfo.ProjectName) && x.Version.Equals(taskInfo.Version) && x.IsRemote);

        if (proj == null)
        {
            proj = GetProject(taskInfo);
        }
        string taskId = taskInfo.Id;
        
        if (!_runningContext.CanAddTask(proj.Id, taskId, out string rejectReason))
        {
            _logService.LogInfo($"任务被拒绝: {rejectReason}");
            return;
        }

        receivedTask = AddTask(proj, taskId, taskInfo.IsVideoLog, taskInfo.Parameters, taskInfo.Token,
            taskInfo.ParentId);
        _logService.LogInfo($"开始下载项目 { taskInfo.ProjectName} v{taskInfo.Version}");
        // Use the concurrent dictionary to ensure single download per project
        string projectKey = GetProjectKey(taskInfo);
        taskInfo.ProjectRoot = proj.FilePath;
        var downloadTask = _activeDownloads.GetOrAdd(projectKey,
            key => DownloadAndExtractProjectAsync(taskInfo, proj));

        bool downloadSuccess = await downloadTask;
        _activeDownloads.TryRemove(projectKey, out _);

        if (!downloadSuccess)
        {
            receivedTask.TaskStopInfo = new TaskStopInfo("下载项目失败", TaskStopType.DownloadError);
            _runningContext.OnTaskCompleted?.Invoke(receivedTask);
            return;
        }

        AddToRunningContext(proj);
        
        var inParameters = taskInfo.Parameters.To<Dictionary<string, object>>();
        await _execManager.StartExecutorAsync(new()
        {
            TaskId = taskInfo.Id,
            ProjectId = proj.Id,
            ServerTaskId = taskInfo.Id,
            BaseUrl = baseUrl,
            InParameters = inParameters,
            Token = taskInfo.Token,
            IsLogDebug = taskInfo.LogType == "DEBUG"
        });

        _logService.LogInfo($"任务 {taskInfo.Id} 启动成功");
    }
    public void AddToRunningContext(RobotProject proj)
    {
        var robotProject = _runningContext.RobotProjects.Find(d => d.Id.Equals(proj.Id));
        if (robotProject == null)
        {
            if (!IsRemote)
            {
                _jsonDbContext.Add(proj);
            }
            _runningContext.RobotProjects.Add(proj);
            _runningContext.OnProjectAdded?.Invoke(proj);
        }
        else
        {
            if (!robotProject.Versions.Exists(d => d.Equals(proj.Version)))
            {
                robotProject.FilePaths.Add(proj.FilePath);
                robotProject.FilePath = proj.FilePath;
                robotProject.Versions.Add(proj.Version);
                robotProject.Version = proj.Version;
                _runningContext.OnProjectUpdated?.Invoke(robotProject);
                if (!IsRemote)
                {
                    _jsonDbContext.Update(robotProject.Id, robotProject);
                }
            }
        }
    }
    private string GetProjectKey(GetTaskResponse taskInfo)
    {
        return $"{taskInfo.ProjectName}_{taskInfo.Version}_{taskInfo.Code}";
    }
    public RobotTask AddTask(RobotProject project,string taskId,bool isVideo, string parameters, string token = "", string parentId = "")
    {
        var task = new RobotTask(taskId,
            project.Id,
            project.Name,
            project.Version,
            DateTime.Now,
            0,
            isVideo,
            project.Category,
            parameters,
            parentId,
            token
        );
        _logService.LogInfo($"添加任务 {taskId} 到执行器");
        _runningContext.RobotTasks.TryAdd(taskId, task);
        _runningContext.TaskLogUploadDict.TryAdd(taskId, task);
        _runningContext.OnTaskAdded?.Invoke(task);

        return task;
    }
    
    private async Task<bool> DownloadAndExtractProjectAsync(GetTaskResponse taskInfo, RobotProject proj)
    {
        try
        {
            if (!File.Exists(proj.ZipFilePath))
            {
                string projectRoot = Path.GetDirectoryName(proj.FilePath);
                if (!Directory.Exists(projectRoot))
                {
                    Directory.CreateDirectory(projectRoot);
                }

                var downloadRes =  await DownLoadProject(BaseUrl,proj);
                if (!downloadRes.IsSuccess)
                {
                    _logService.LogError($"项目下载失败: { downloadRes.ErrorMessage}");
                    return false;
                }
                _logService.LogInfo($"项目下载成功: {proj.Name} v{proj.Version}");
            }

            if (!Directory.Exists(proj.FilePath))
            {
                Directory.CreateDirectory(proj.FilePath);
                FileHelper.ExtractZipFile(proj.ZipFilePath, taskInfo.ProjectRoot, out _);
            }
            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"下载和解压过程出错: {ex.Message}", ex);
            return false;
        }
    }
        public (bool IsSuccess, string ErrorMessage) RemoteImport(string zipPath, RobotProject robotProject)
    {
        string tempPath = _fileStorageConfig.TempPath;

        #region 检查是否包含project.json并复制到用户目录

        bool isUnzipped = false;
        try
        {
            using var archive = ZipFile.OpenRead(zipPath);

            if (archive.Entries.Any(entry => entry.FullName.Contains("project.json")))
            {
                isUnzipped = FileHelper.ExtractZipFile(zipPath, tempPath, out string errorMessage);
            }
            else
            {
                return (false, $"该项目包不存在project.json文件");
            }
        }
        catch (Exception ex)
        {
            return (false, $"下载项目文件失败: {ex.ToString()}");
        }

        #endregion 检查是否包含project.json并复制到用户目录

        #region 读取并反序列化 project.json

        // 读取并反序列化 project.json
        var directories = Directory.GetDirectories(tempPath);
        var files = Directory.GetFiles(tempPath);
        if (directories.Count() == 1 && files.Count() == 0)
        {
            FileHelper.CopyAll(new DirectoryInfo(directories[0]), new DirectoryInfo(tempPath));
            Directory.Delete(directories[0], true);
        }
        var newDirectories = Directory.GetDirectories(tempPath);
        foreach (var item in newDirectories)
        {
            string name = Path.GetFileName(item); // 注意：使用 GetFileName 获取文件夹名
            if (name.Equals(".packages")) // 检查是否是目标文件夹（可能需要修正拼写）
            {
                string parentPath = Path.GetDirectoryName(item);
                string newPath = Path.Combine(parentPath, "packages"); // 指定新的文件夹名

                try
                {
                    Directory.Move(item, newPath); // 执行重命名操作
                    Console.WriteLine($"已将文件夹 {item} 重命名为 {newPath}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"重命名文件夹时出错: {ex.Message}");
                }
            }
        }
        // 读取并反序列化 project.json
        string projectJsonPath = Path.Combine(tempPath, "project.json");
        if (!File.Exists(projectJsonPath))
            return (false, "缺失项目文件: project.json");

        ProjectModel project;
        try
        {
            string projectJson = File.ReadAllText(projectJsonPath);
            project = JsonConvert.DeserializeObject<ProjectModel>(projectJson);
        }
        catch (Exception ex)
        {
            return (false, $"解析项目文件失败: {ex.Message}");
        }

        #endregion 读取并反序列化 project.json

        #region 复制文件

        // 复制文件
        try
        {
            File.Delete(zipPath);
            FileHelper.CreateZip(robotProject.ZipFilePath, tempPath);
        }
        catch (Exception ex)
        {
            return (false, $"文件复制失败: {ex.Message}");
        }
        finally
        {
            // 删除临时文件
            Directory.Delete(tempPath, true);
        }

        #endregion 复制文件

        return (true, "导入成功");
    }
    public string GetProjectDirName(bool isRemote, string name, string code, string version)
    {
        string res = string.Format("{0}-{1}-{2}-{3}", isRemote ? "R" : "L", name, code, version);
        return res;
    }
    private RobotProject GetProject(GetTaskResponse taskInfo)
    {
        // 构建项目路径和实例
        string projectDirName = GetProjectDirName(true, taskInfo.ProjectName, taskInfo.Code, taskInfo.Version);
        string projectPath = Path.Combine(_fileStorageConfig.ProjectBasePath, projectDirName);
        var robotProject = new RobotProject(
            id: taskInfo.ProjectId,
            projectContentId: taskInfo.ProjectContentId,
            isRemote: true,
            code: taskInfo.Code,
            name: taskInfo.ProjectName,
            version: taskInfo.Version,
            filePath: projectPath,
            description: taskInfo.Description,
            tags: taskInfo.Label == null ? new List<string>() : taskInfo.Label.Split('/').ToList(),
            category:1,
            parentId: taskInfo.ParentId
        );
        robotProject.FilePaths.Add(robotProject.FilePath);
        robotProject.Versions.Add(robotProject.Version);
        return robotProject;
    }
            public async Task<(bool IsSuccess, string ErrorMessage, RobotProject Project, string projectFolder)> LocalImport(string zipPath, bool isRemote, string alias, string version)
        {
            string tempPath = _fileStorageConfig.TempPath;
            if (Directory.Exists(tempPath))
            {
                Directory.Delete(tempPath, true);
            }
            Directory.CreateDirectory(tempPath);
            #region 解压并检查是否包含project.json

            // 解压并检查是否包含 project.json
            bool isUnzipped = false;
            try
            {
                using var archive = ZipFile.OpenRead(zipPath);
                if (archive.Entries.Any(entry => entry.FullName.Contains("project.json")))
                {
                    ZipFile.ExtractToDirectory(zipPath, tempPath, Encoding.GetEncoding("GB2312"));
                    isUnzipped = true;
                }
            }
            catch (Exception ex)
            {
                return (false, $"解压失败: {ex.Message}", null, string.Empty);
            }

            if (!isUnzipped)
                return (false, "无法读取到项目文件，导入失败", null, string.Empty);

            #endregion 解压并检查是否包含project.json

            #region 读取并反序列化 project.json

            // 读取并反序列化 project.json
            var directories = Directory.GetDirectories(tempPath);
            var files = Directory.GetFiles(tempPath);
            if (directories.Count() == 1 && files.Count() == 0)
            {
                FileHelper.CopyAll(new DirectoryInfo(directories[0]), new DirectoryInfo(tempPath));
                Directory.Delete(directories[0], true);
            }
            var newDirectories = Directory.GetDirectories(tempPath);
            foreach (var item in newDirectories)
            {
                string name = Path.GetFileName(item); // 注意：使用 GetFileName 获取文件夹名
                if (name.Equals(".packages")) // 检查是否是目标文件夹（可能需要修正拼写）
                {
                    string parentPath = Path.GetDirectoryName(item);
                    string newPath = Path.Combine(parentPath, "packages"); // 指定新的文件夹名

                    try
                    {
                        Directory.Move(item, newPath); // 执行重命名操作
                        Console.WriteLine($"已将文件夹 {item} 重命名为 {newPath}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"重命名文件夹时出错: {ex.Message}");
                    }
                }
            }
            string projectJsonPath = Path.Combine(tempPath, "project.json");
            if (!File.Exists(projectJsonPath))
                return (false, "缺失项目文件: project.json", null, string.Empty);

            ProjectModel project;
            try
            {
                string projectJson = File.ReadAllText(projectJsonPath);
                project = JsonConvert.DeserializeObject<ProjectModel>(projectJson);
            }
            catch (Exception ex)
            {
                return (false, $"解析项目文件失败: {ex.Message}", null, string.Empty);
            }

            #endregion 读取并反序列化 project.json
            
            #region 构建项目路径和实例

            // 构建项目路径和实例
            string projectName = project.Name;
            if (project.PublishLocalPackages == null)
            {
                project.PublishLocalPackages = new ProjectPublishLocal();
                project.PublishLocalPackages.PublishVersion = "1.0.0";
            }
            string projectVersion = project.PublishLocalPackages.PublishVersion;
            string projectPath = Path.Combine(
                _fileStorageConfig.ProjectPath,
                GetProjectDirName(false, projectName, project.Code, projectVersion)
            );

            var proj = new RobotProject(
                id: project.Id,
                projectContentId: string.Empty,
                isRemote: false,
                code: project.Code,
                name: projectName,
                version: projectVersion,
                filePath: projectPath,
                description: project.Description,
                tags: project.Tags,
                category: project.Category,
                parentId: string.Empty
            );
            proj.FilePaths.Add(proj.FilePath);
            proj.Versions.Add(proj.Version);

            #endregion 构建项目路径和实例

            #region 复制文件

            // 复制文件
            try
            {
                if (!Directory.Exists(projectPath))
                {
                    FileHelper.CopyAll(new DirectoryInfo(tempPath), new DirectoryInfo(projectPath));
                }
            }
            catch (Exception ex)
            {
                return (false, $"文件复制失败: {ex.Message}", null, string.Empty);
            }
            finally
            {
                // 删除临时文件
                Directory.Delete(tempPath, true);
            }

            #endregion 复制文件

            #region 更新运行上下文

            AddToRunningContext(proj);

            #endregion 更新运行上下文
            
            return (true, "导入成功", proj, projectPath);
        }
    public async Task Log(TaskLog taskLog)
    {
        LocalLogTask(taskLog);

        await LogProcess(taskLog);
    }
    
    private void LocalLogTask(TaskLog taskLog)
    {
        try
        {
            var task = _runningContext.RobotTasks.FirstOrDefault(x => x.Key.Equals(taskLog.TaskId)).Value;
            if (task == null)
            {
                task =  _runningContext.TaskLogUploadDict.FirstOrDefault(x => x.Key.Equals(taskLog.TaskId)).Value;
                if(task == null)
                {
                    return;
                }
            }
            var project = _runningContext.RobotProjects.Find(x => x.Id.Equals(task.ProjectId));
            if (project == null)
            {
                return;
            }
            if (project.IsRemote)
            {
                return;
            }

            string logFileName = Path.Combine(_fileStorageConfig.LogPath, "projects", $"{(project.IsRemote ? "S" : "L")}-{task.ProjectName}-{task.ProjectVersion}", $"{task.StartTime:yyyy-MM-dd-hhmmss}.txt");
            //打印全部日志
            if (taskLog.traceInfo != null && taskLog.traceInfo.TraceLevel != "Error")
                _logService.LogInfo(logFileName, taskLog.traceInfo.Message);
            else if (taskLog.traceInfo != null)
            {
                _logService.LogInfo(logFileName, taskLog.traceInfo.Message);

                logFileName = Path.Combine(_fileStorageConfig.LogPath, "projects", $"{(project.IsRemote ? "S" : "L")}-{project.Name}-{project.Version}-error.txt");
                if (taskLog.traceInfo.Equals("Error"))
                    _logService.LogError(logFileName, taskLog.traceInfo.Message);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理日志本地出错:{ex.ToString()}");
        }
    }
    public void Dispose()
    {
        StopCurrentMode();
        _pollingCancellationTokenSource?.Dispose();
    }
}