﻿using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public class RobotLogService : IRobotLogService
    {
        public Task SendActivityLogAsync(ActivityLogModel activityLogModel)
        {
            throw new NotImplementedException();
        }

        public Task SendTaskLogAsync(TaskLogModel taskLogModel)
        {
            throw new NotImplementedException();
        }
    }
}