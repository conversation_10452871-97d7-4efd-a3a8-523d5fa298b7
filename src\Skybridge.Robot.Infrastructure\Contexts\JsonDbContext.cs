using JsonFlatFileDataStore;
using Skybridge.Domain.Core;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.Contexts;

/// <summary>
/// JSON数据库上下文，负责数据的持久化存储
/// </summary>
public sealed class JsonDbContext : IDisposable
{
    private readonly DataStore _store;
    private readonly ILogService _logService;
    private bool _disposed;

    public JsonDbContext(IFileStorageConfig fileStorageConfig, ILogService logService)
    {
        _logService = logService ?? throw new ArgumentNullException(nameof(logService));
        var dbPath = Path.Combine(fileStorageConfig.BasePath, "robotDb.json");
        _store = new DataStore(dbPath, true, "_id");

        // 清理旧的任务数据
        try
        {
            _store.GetCollection<RobotTask>().DeleteMany(x => true);
            _logService.LogInfo("Cleaned up old task data");
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to clean up old task data",ex);
        }
    }

    /// <summary>
    /// 获取指定类型的所有数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <returns>数据列表</returns>
    public IEnumerable<T> Get<T>() where T : class
    {
        ThrowIfDisposed();
        try
        {
            return _store.GetCollection<T>().AsQueryable();
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to get data of type {typeof(T).Name}", ex);
            return Enumerable.Empty<T>();
        }
    }

    /// <summary>
    /// 添加数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="item">数据项</param>
    public void Add<T>(T item) where T : class
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(item);

        try
        {
            _store.GetCollection<T>().InsertOne(item);
            Console.WriteLine($"Added item of type {typeof(T).Name}");
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to add item of type {typeof(T).Name}", ex);
            throw;
        }
    }

    /// <summary>
    /// 更新数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="id">数据ID</param>
    /// <param name="item">数据项</param>
    public void Update<T>(string id, T item) where T : class, IEntity
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(id);
        ArgumentNullException.ThrowIfNull(item);

        try
        {
            _store.GetCollection<T>().UpdateOne(x => x.Id.Equals(id), item);
            Console.WriteLine($"Updated item of type { typeof(T).Name} with ID {id}");
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to update item of type { typeof(T).Name} with ID {id}",ex );
            throw;
        }
    }

    /// <summary>
    /// 删除数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="id">数据ID</param>
    public void Delete<T>(string id) where T : class, IEntity
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(id);

        try
        {
            _store.GetCollection<T>().DeleteOne(x => x.Id == id);
            Console.WriteLine($"Deleted item of type { typeof(T).Name} with ID {id}");
        }
        catch (Exception ex)
        {
            _logService.LogError( $"Failed to delete item of type {typeof(T).Name} with ID {id}",ex );
            throw;
        }
    }

    private void ThrowIfDisposed()
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(JsonDbContext));
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            _store.Dispose();
        }
        catch (Exception ex)
        {
            _logService.LogError( "Error during JsonDbContext disposal",ex);
        }
        finally
        {
            _disposed = true;
        }
    }
}