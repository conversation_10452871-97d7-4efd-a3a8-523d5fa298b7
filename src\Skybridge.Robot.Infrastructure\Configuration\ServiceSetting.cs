﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Skybridge.Robot.Infrastructure.Configuration
{
    public class ServiceSetting
    {
        /// <summary>
        ///     登录接口
        /// </summary>
        public string LoginApi { get; } = "api/robot/login";

        public string FaceLoginApi { get; } = "api/identification/login";

        public string GetSkill { get; } = "api/robot/get_skill";

        public string SaveTask { get; } = "api/task_cron_high/save";

        /// <summary>
        ///     任务文件下载接口
        /// </summary>
        public string DownloadApi { get; } = "api/task/download";

        /// <summary>
        ///     机器人注册接口
        /// </summary>
        public string RegisterApi { get; } = "api/rpa/register";

        /// <summary>
        ///     任务日志保存接口
        /// </summary>
        public string TaskLogApi { get; } = "api/task_log/save";

        public string ActivityLogApi { get; } = "api/client_activity_log/save";

        public string TaskUploadFileApi { get; } = "api/client_activity_log/save";

        public string TaskUploadLogApi { get; } = "api/client_activity_log/fileSave";

        /// <summary>
        ///     机器人状态改变通知接口
        /// </summary>
        public string StateChangeApi { get; } = "api/rpa/update_workstate";

        /// <summary>
        ///     服务器状态检测接口
        /// </summary>
        public string GetServerState { get; } = "api/service/state";

        /// <summary>
        ///     版本检测/更新接口
        /// </summary>
        public string FindVersionApi { get; } = "api/client_app_version/list";

        /// <summary>
        ///     新版本文件下载接口
        /// </summary>
        public string DownloadAppApi { get; } = "api/client_app_version/download";

        /// <summary>
        ///     主动轮询任务接口
        /// </summary>
        public string GetTaskApi { get; } = "api/task/get";

        public string GetTaskLogExist { get; } = "api/task_log/exist";
        public string CreateTaskApi { get; } = "api/task/create";
        public string GetActiveProjectInfoApi { get; } = "api/project_content/active";

        public ServiceSetting()
        {
        }
    }
}