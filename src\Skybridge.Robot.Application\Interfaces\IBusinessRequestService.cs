﻿using Skybridge.Robot.Application.DTOs;
using Skybridge.Robot.Application.DTOs.Robot.Requests;
using Skybridge.Robot.Application.DTOs.Robot.Responses;
using Skybridge.Robot.Application.DTOs.Task.Requests;
using Skybridge.Robot.Application.DTOs.Task.Responses;

namespace Skybridge.Robot.Application.Interfaces
{
    public interface IBusinessRequestService
    {
        Task<bool> RegisterRobotAsync(string baseUrl, RobotRegisterRequest robotRegisterInfo);

        Task<GetTaskResponse> GetTaskAsync(string baseUrl);
        
        Task<(bool IsSuccess, string ErrorMessage)> DownloadFileAsync(string baseUrl, string projectContentId,
            string path);

        Task<bool> TaskLogAsync(string baseUrl, TaskLogRequest taskLog);

        Task<bool> UploadActivityLog(string baseUrl, string activityLogContent);

        Task<string> WorkStateAsync(string baseUrl, int state);

        Task<bool> GetServerStateAsync(string baseUrl);
        Task<bool> GetServerConnectStateAsync(string baseUrl);
        

        Task<string> GetTaskLogExist(string baseUrl, string signGuid);

        Task<BusinessResult<CreateTaskResponse>?> CreateTask(string baseUrl,CreateTaskRequest createTaskRequest);

        Task<BusinessResult<GetActiveProjectInfoResponse>?> GetActiveProjectInfo(string baseUrl, string projectId);
    }
}