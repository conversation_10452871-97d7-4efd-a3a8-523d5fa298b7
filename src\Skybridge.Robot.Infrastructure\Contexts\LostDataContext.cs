﻿using Newtonsoft.Json;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Enums;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.DTOs.Task.Requests;
using Skybridge.Robot.Application.DTOs.Task.Responses;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.Contexts
{
    public class LostDataContext : IDisposable
    {
        private readonly ILostDataRepository _lostDataRepository;
        private readonly IBusinessRequestService _businessRequestService;
        private readonly RobotConfig _robot;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private Task _uploadTask;
        private readonly RobotContext _robotContext;
        private readonly ILogService _logService;
        private const int UPLOAD_RETRY_DELAY = 5000; // 5 seconds

        public LostDataContext(ILostDataRepository lostDataRepository, IBusinessRequestService businessRequestService, IAppConfigManager appConfigManager, RobotContext robotContext, ILogService logService)
        {
            _robotContext = robotContext;
            _logService = logService;
            _lostDataRepository = lostDataRepository ?? throw new ArgumentNullException(nameof(lostDataRepository));
            _businessRequestService = businessRequestService ?? throw new ArgumentNullException(nameof(businessRequestService));
            _robot = appConfigManager.RobotConfig;
            _cancellationTokenSource = new CancellationTokenSource();
        }

        public void StartLostDataUploadTask()
        {
            if (_uploadTask != null)
            {
                return; // Task already running
            }

            _uploadTask = Task.Run(async () =>
            {
                while (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    try
                    {
                        if (!_robotContext.IsBusy && _robot.Mode == Mode.RemoteServer)
                        {
                            await UploadLostData(_cancellationTokenSource.Token);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logService.LogError($"Lost data upload error: {ex.Message}");
                    }

                    try
                    {
                        await Task.Delay(UPLOAD_RETRY_DELAY, _cancellationTokenSource.Token);
                    }
                    catch (TaskCanceledException)
                    {
                        break;
                    }
                }
            }, _cancellationTokenSource.Token);
        }

        public async Task UploadLostData(CancellationToken cancellationToken = default)
        {
            var lostDatas = await _lostDataRepository.GetAllAsync();

            foreach (var lostData in lostDatas)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    break;
                }

                try
                {
                    bool uploadSuccess = await ProcessLostDataItem(lostData);
                    if (uploadSuccess)
                    {
                        await CleanupAfterSuccess(lostData);
                    }
                }
                catch (Exception ex)
                {
                    _logService.LogError($"Error processing lost data item {lostData.Id}: {ex.ToString()}");
                }
            }
        }

        private async Task<bool> ProcessLostDataItem(LostData lostData)
        {
            switch (lostData.Type)
            {
                case LogType.Activity:
                    return await _businessRequestService.UploadActivityLog(_robot.ServerUrl, lostData.Content);

                case LogType.Task:
                    string getRes = string.Empty;
                    try
                    {
                        getRes = await _businessRequestService.GetTaskLogExist(_robot.ServerUrl, lostData.SignGuid);
                        var result = JsonConvert.DeserializeObject<GetTaskLogExistResponse>(getRes);
                        if (result.Code != "200")
                        {
                            var taskLogRequest = JsonConvert.DeserializeObject<TaskLogRequest>(lostData.Content);
                            if (taskLogRequest == null)
                            {
                                return false;
                            }
                            taskLogRequest.Details = "网络波动重新上传：" + taskLogRequest.Details;
                            string value = JsonConvert.SerializeObject(taskLogRequest);
                            var res = await _businessRequestService.TaskLogAsync(_robot.ServerUrl, taskLogRequest);
                            if (!res)
                            {
                                _logService.LogError($"上传丢失的任务日志{lostData.TaskId}失败，内容：{value}, 获取任务日志结果响应内容：{getRes}");
                                bool isServerTask = int.TryParse(lostData.TaskId, out _);
                                if (!isServerTask)
                                {
                                    return true;
                                }
                            }
                            else
                            {
                                _logService.LogError($"上传丢失的任务日志{lostData.TaskId}");
                            }
                            return res;
                        }
                        else
                        {
                            //检查到已经存在了不需要再上传任务日志
                            _logService.LogInfo($"检查到{lostData.TaskId}已经存在的任务日志，无需再上传");
                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logService.LogError($"上传的日志内容{lostData.Content},获取任务日志结果响应内容:{getRes},丢失的任务日志上传出现错误：{ex.ToString()}");
                        return false;
                    }

                default:
                    _logService.LogError($"Unknown lost data type: {lostData.Type}");
                    return false;
            }
        }

        private async Task CleanupAfterSuccess(LostData lostData)
        {
            await _lostDataRepository.DeleteAsync(lostData.Id);

            if (!string.IsNullOrEmpty(lostData.LostFilePath))
            {
                await CleanupVideoFiles(lostData.LostFilePath);
            }
        }

        private async Task CleanupVideoFiles(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                string dirPath = Path.GetDirectoryName(filePath);
                if (Directory.Exists(dirPath))
                {
                    var files = Directory.GetFiles(dirPath, "*", SearchOption.AllDirectories);
                    if (files.Length == 0)
                    {
                        await Task.Run(() => Directory.Delete(dirPath, true));
                    }
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"Error cleaning up video files: {ex.Message}");
            }
        }

        public void Dispose()
        {
            try
            {
                _cancellationTokenSource?.Cancel();
                _uploadTask?.Wait(TimeSpan.FromSeconds(5));
            }
            catch (Exception ex)
            {
                _logService.LogError($"Error disposing LostDataService: {ex.Message}");
            }
            finally
            {
                _cancellationTokenSource?.Dispose();
            }
        }
    }
}