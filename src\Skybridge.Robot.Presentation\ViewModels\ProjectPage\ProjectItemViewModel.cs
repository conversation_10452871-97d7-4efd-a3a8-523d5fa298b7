﻿using ReactiveUI;
using Skybridge.Controls;
using Skybridge.Domain.Core.Exector;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using Avalonia.Controls.ApplicationLifetimes;
using FluentAvalonia.UI.Controls;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using Skybridge.Domain.Core.Model;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.DTOs.Robot.Requests;
using Skybridge.Robot.Application.DTOs.Robot.Responses;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Presentation.Services;
using Skybridge.Robot.Presentation.Views;

namespace Skybridge.Robot.Presentation.ViewModels.ProjectPage
{
    public class ProjectItemViewModel : ViewModelBase
    {
        private bool _IsShow;

        public bool IsShow
        {
            get => _IsShow;
            set => this.RaiseAndSetIfChanged(ref _IsShow, value);
        }

        private bool _IsShowRun;

        public bool IsShowBtn
        {
            get => _IsShowRun;
            set => this.RaiseAndSetIfChanged(ref _IsShowRun, value);
        }

        private string _name = string.Empty;

        public string Name
        {
            get => _name;
            set => this.RaiseAndSetIfChanged(ref _name, value);
        }

        private Tuple<string, string> _version;

        public Tuple<string, string> Version
        {
            get => _version;
            set => this.RaiseAndSetIfChanged(ref _version, value);
        }

        private string _icon;

        public string Icon
        {
            get => _icon;
            set => this.RaiseAndSetIfChanged(ref _icon, value);
        }

        private bool _isShowRun;

        public bool IsShowRun
        {
            get => _isShowRun;
            set => this.RaiseAndSetIfChanged(ref _isShowRun, value);
        }

        private ObservableCollection<Tuple<string, string>> _versions;

        public ObservableCollection<Tuple<string, string>> Versions
        {
            get => _versions;
            set => this.RaiseAndSetIfChanged(ref _versions, value);
        }

        private ObservableCollection<RunArgumentViewModel> _runArguments = new();

        public ObservableCollection<RunArgumentViewModel> RunArguments
        {
            get => _runArguments;
            set => this.RaiseAndSetIfChanged(ref _runArguments, value);
        }

        private bool openFlyout = false;

        private readonly IProjectManager _projectManager;
        private readonly IExecManager _execManager;
        private readonly ITaskManager _taskManager;
        private readonly RobotContext _robotContext;
        private readonly IBusinessRequestService _businessRequestService;
        private readonly RobotProject _robotProject;
        private readonly DialogService _dialogService;
        private readonly RunningContext _runningContext;
        private readonly ILogService _logService;

        public ProjectItemViewModel(RobotProject robotProject,IProjectManager projectManager, 
            IExecManager execManager, ITaskManager taskManager,RobotContext robotContext,
            IBusinessRequestService businessRequestService,DialogService dialogService,
            RunningContext runningContext,ILogService logService)
        {
            _robotProject = robotProject;
            _businessRequestService = businessRequestService;
            _robotContext = robotContext;
            _projectManager = projectManager;
            _execManager = execManager;
            _taskManager = taskManager;
            _dialogService = dialogService;
            _runningContext = runningContext;
            _logService = logService;
            ToggleShowRunCommand = ReactiveCommand.CreateFromTask(StartTask);
            CloseFlyoutCommand = ReactiveCommand.Create(CloseFlyout);
            OpenFlyoutCommand = ReactiveCommand.Create(OpenFlyout);
            ShowBtnCommand = ReactiveCommand.Create(ShowBtn);
            HiddenBtnCommand = ReactiveCommand.Create(HiddenBtn);
            DelProjectCommand = ReactiveCommand.CreateFromTask(DelProject);
        }

        // public ReactiveCommand<Unit, Unit> ShowTasksCommand => ReactiveCommand.Create(ShowTasks);
        public ReactiveCommand<Unit, Unit> ToggleShowRunCommand { get; }

        public ReactiveCommand<Unit, Unit> CloseFlyoutCommand { get; }
        public ReactiveCommand<Unit, Unit> OpenFlyoutCommand { get; }
        public ReactiveCommand<Unit, Unit> ShowBtnCommand { get; }
        public ReactiveCommand<Unit, Unit> HiddenBtnCommand { get; }
        public ReactiveCommand<Unit, bool> DelProjectCommand { get; }

        // private void ShowTasks()
        // {
        // var mainViewModel = Ioc.Default.GetRequiredService<MainWindowViewModel>();
        // mainViewModel.SelectedItem = mainViewModel.Routers.First(x => x.Name == "任务");
        // }

        private async Task StartTask()
        {
            RunArguments.Clear();
            if (_robotContext.IsRemote && _robotProject.IsRemote)
            {
                await CreateRemoteTask();
            }
            else
            {
                await CreateLocalTask();
            }
        }

        private async Task CreateLocalTask()
        {
            var robotProject = _runningContext.RobotProjects.Find(d => d.Id.Equals(_robotProject.Id));
            if (robotProject == null)
            {
                return;
            }
            if ( !Directory.Exists(robotProject.FilePath))
            {
                await _dialogService.ShowConfirmAsync("提醒",$"项目文件已经丢失，该项目将被清理" );
                _projectManager.DelProject(robotProject.Id);
                return;
            }
            var filePath = robotProject.FilePaths.Find(d => d.Contains(robotProject.Version));
            if (filePath == null)
            {
                await _dialogService.ShowMessageAsync("提示",$"未找到可执行项目文件{filePath}");
                return;
            }
            robotProject.FilePath = filePath;
            GetArguments(robotProject);
            var view = new ParameterView();
            var viewModel = new ParameterViewModel(_runArguments);
            var cd = new ContentDialog()
            {
                Title = "启动参数",
                Content = view,
                DataContext = viewModel,
                PrimaryButtonText = "确定",
                SecondaryButtonText = "取消"
            };
            var cdRes = await cd.ShowAsync();
            if (cdRes == ContentDialogResult.Primary)
            {
                IDictionary<string, object> kvbArguments = new Dictionary<string, object>();
                foreach (var argument in _runArguments)
                {
                    kvbArguments.Add(argument.Name, argument.Value);
                }
                bool isDebugLog = viewModel.IsLogDebug;
                var taskId = Guid.NewGuid().ToString();
                List<RunArgumentModel> runArgumentModels = _runArguments.Select(x => new RunArgumentModel
                {
                    Name = x.Name,
                    Value = x.Value,
                }).ToList();
                string parameters = JsonConvert.SerializeObject(runArgumentModels);
                await File.WriteAllTextAsync(robotProject.ParametersPath, parameters);
                 _robotContext.AddTask(_robotProject, taskId, false, parameters);
                await LocalStartTask(taskId,isDebugLog);
            }
            
         
        }

        private void GetArguments(RobotProject robotProject)
        {
            _runArguments = GetLocalArguments(robotProject);
            List<RunArgumentModel>? runArguments = new List<RunArgumentModel>();
            if (File.Exists(robotProject.ParametersPath))
            {
                try
                {
                    string parameterString = File.ReadAllText(robotProject.ParametersPath);
                    runArguments =
                        JsonConvert.DeserializeObject<List<RunArgumentModel>>(parameterString) ?? new();
                }
                catch (Exception ex)
                {
                    _logService.LogError($"参数文件读取异常:{ex}");
                }
            }

            foreach (var item in _runArguments)
            {
                if (runArguments != null )
                {
                    var storeArg = runArguments.FirstOrDefault(d => d.Name.Equals(item.Name));
                    if (storeArg != null)
                    {
                        item.Value = storeArg.Value;
                    }
                }
            }
        }

        private ObservableCollection<RunArgumentViewModel> GetLocalArguments(RobotProject project)
        {
            var argumentModels = GetArgumentModels(project.Id);

            var arguments = 
                argumentModels?.Select(x => new RunArgumentViewModel()
            {
                Name = x.ArgumentName,
                Value = x.ArgumentValue?.ToString() ?? "",
                Type = x.ArgumentType,
                Annotation = x.Annotation
            });
            return new ObservableCollection<RunArgumentViewModel>(arguments ?? Array.Empty<RunArgumentViewModel>());
        }
        
        public List<ArgumentModel> GetArgumentModels(string id)
        {
            var project = _runningContext.RobotProjects.Find(x => x.Id.Equals(id));
            if (project == null)
            {
                return new List<ArgumentModel>();
            }
            string projectFilePath = Path.Combine(project.FilePath, "project.json");
            string projectJson = File.ReadAllText(projectFilePath);
            ProjectModel projectModel = JsonConvert.DeserializeObject<ProjectModel>(projectJson);
            return projectModel.ArgumentsValues;
        }

        private async Task CreateRemoteTask()
        {
            //获取当前项目的版本信息
            var result = await _businessRequestService.GetActiveProjectInfo(_robotContext.BaseUrl, _robotProject.Id);
            if (result == null || result.Code != 200 || result.Data == null)
            {
                return;
            }
            RunArguments = GetRemoteArguments(result.Data.Params);
            var view = new ParameterView();
            var viewModel = new ParameterViewModel(RunArguments);
            var cd = new ContentDialog()
            {
                Title = "启动参数",
                Content = view,
                DataContext = viewModel,
                PrimaryButtonText = "确定",
                SecondaryButtonText = "取消"
            };
            var cdRes = await cd.ShowAsync();
            if (cdRes == ContentDialogResult.Primary)
            {
                await RemoteStartTask(result.Data.Code, viewModel.IsLogDebug);
            }
        }

        private async Task RemoteStartTask(string code,bool isDebugLog = false)
        {
            var paramDict = RunArguments.ToDictionary(x => x.Name, x => x.Value);
            var @params=JsonConvert.SerializeObject(paramDict);
            //创建任务所需的参数
            CreateTaskRequest request = new CreateTaskRequest
            {
                Rpa = _robotContext.Id,
                Params = @params,
                Code = code,
                IsVedio = "0",
                LogType = isDebugLog ? "DEBUG" : "INFO"
            };
            await _businessRequestService.CreateTask(_robotContext.BaseUrl,request);
        }
        public ObservableCollection<RunArgumentViewModel> GetRemoteArguments(List<ParamsInfo> paramsInfos)
        {
            ObservableCollection<RunArgumentViewModel> arguments = new ObservableCollection<RunArgumentViewModel>();
            foreach (var param in paramsInfos)
            {
                RunArgumentViewModel argument = new RunArgumentViewModel()
                {
                    Name = param.Name,
                    Type = param.Type
                };
                arguments.Add(argument);
            }
            return arguments;
        }
        private void CloseFlyout()
        {
            openFlyout = false;
            IsShow = false;
        }

        private void OpenFlyout()
        {
            openFlyout = true;
        }

        private void ShowBtn()
        {
            IsShow = true;
        }

        private void HiddenBtn()
        {
            if (!openFlyout)
                IsShow = false;
        }


        private async Task LocalStartTask(string taskId,bool isLogDebug)
        {
            await _execManager.StartExecutorAsync(new()
            {
                TaskId = taskId,
                ProjectId = Version.Item1,
                BaseUrl = "",
                InParameters = RunArguments.ToDictionary(x => x.Name, x => (object)x.Value),
                IsLogDebug = isLogDebug
            });
        } 

        private async Task<bool> DelProject()
        {
            var result = await MessageBox.ShowOverlayAsync("是否删除项目", "提示信息");
            return result.Equals(MessageBoxResult.OK);
        }
    }
}