﻿using Newtonsoft.Json;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.DTOs;
using Skybridge.Robot.Application.DTOs.Robot.Requests;
using Skybridge.Robot.Application.DTOs.Robot.Responses;
using Skybridge.Robot.Application.DTOs.Task.Requests;
using Skybridge.Robot.Application.DTOs.Task.Responses;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public class BusinessRequestService : IBusinessRequestService
    {
        private readonly ServiceSetting _serviceSetting;
        private readonly IHttpClientService _httpClientService;
        private readonly ILogService _logService;
        public BusinessRequestService(IHttpClientService httpClientService,IAppConfigManager appConfigManager, ServiceSetting serviceSetting, ILogService logService)
        {
            _httpClientService = httpClientService;
            _logService = logService;
            _serviceSetting = serviceSetting;
            // 延迟初始化 RobotId
            string robotId = appConfigManager.RobotConfig.RobotId;
            var headerDict = new Dictionary<string, string>
        {
            { "Authorization", robotId }
        };
            _httpClientService.AddHeader(headerDict);
            _httpClientService.SetTimeout(5000);
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DownloadFileAsync(string baseUrl, string projectContentId, string path)
        {

            if (!baseUrl.EndsWith("/"))
                baseUrl = baseUrl + "/";
            var url = baseUrl + _serviceSetting.DownloadApi;
            return await _httpClientService.DownloadFileAsync($"{url}?project_content_id={projectContentId}", path);
        }
        public async Task<GetTaskResponse> GetTaskAsync(string baseUrl)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";
                var url = baseUrl + _serviceSetting.GetTaskApi;
                var resultStr = await _httpClientService.GetAsync(url);
                var result = JsonConvert.DeserializeObject<BusinessResult<GetTaskResponse>>(resultStr);
                return result?.Data;
            }
            catch (Exception e)
            {
                Console.WriteLine("请求服务器失败,请检查服务器地址");
                return null;
            }
        }

        public async Task<bool> RegisterRobotAsync(string baseUrl, RobotRegisterRequest robotRegisterInfo)
        {
            if (!baseUrl.EndsWith("/"))
                baseUrl = baseUrl + "/";
            var url = baseUrl + _serviceSetting.RegisterApi;
            var resultStr = await _httpClientService.PostAsync(url, JsonConvert.SerializeObject(robotRegisterInfo));
            var result = JsonConvert.DeserializeObject<BusinessResult<string>>(resultStr);
            return result?.Status == "success";
        }

        public async Task<bool>  GetServerStateAsync(string baseUrl)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";
                var url = baseUrl + _serviceSetting.GetServerState;
                string resultStr = await _httpClientService.GetAsync(url);
                var result = JsonConvert.DeserializeObject<BusinessResult<string>>(resultStr);
                if (result.Status == "success")
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (InvalidOperationException)
            {
                //uri无效的问题 直接返回false 不需要记录日志
                return false;
            }
            catch (Exception ex)
            {
                _logService.LogError($"GetServerStateAsync failed: {ex.ToString()}");
                return false;
            }
        }

        public async Task<bool> GetServerConnectStateAsync(string baseUrl)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";
                var url = baseUrl + _serviceSetting.GetServerState;
                string resultStr = await _httpClientService.GetAsync(url);
                var result = JsonConvert.DeserializeObject<BusinessResult<string>>(resultStr);
                if (result?.Status == "success" || result?.Status == "failed")
                {
                    return true;
                }
                return false;
            }
            catch (InvalidOperationException)
            {
                //uri无效的问题 直接返回false 不需要记录日志
                return false;
            }
            catch (Exception ex)
            {
                _logService.LogError($"GetServerConnectStateAsync failed: {ex.ToString()}");
                return false;
            }
        }

        public async Task<string> GetTaskLogExist(string baseUrl, string signGuid)
        {
            if (!baseUrl.EndsWith("/"))
                baseUrl = baseUrl + "/";
            var url = baseUrl + _serviceSetting.GetTaskLogExist;
            url += $"/{signGuid}";

            var res = await _httpClientService.GetAsync(url);
            return res;
        }

        public async Task<BusinessResult<CreateTaskResponse>?> CreateTask(string baseUrl, CreateTaskRequest createTaskRequest)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl += "/";
                var url = baseUrl + _serviceSetting.CreateTaskApi;
                var resultStr = await _httpClientService.PostAsync(url, JsonConvert.SerializeObject(createTaskRequest));
                var result = JsonConvert.DeserializeObject<BusinessResult<CreateTaskResponse>>(resultStr);
                return result;
            }
            catch (Exception ex)
            {
                return new BusinessResult<CreateTaskResponse>()
                {
                    Status = "error",
                    Message = $"创建任务失败：{ex.Message}",
                    Data = null
                };
            }

        }

        public async Task<BusinessResult<GetActiveProjectInfoResponse>?> GetActiveProjectInfo(string baseUrl, string projectId)
        {
            try
            {
                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";
                var url = baseUrl + _serviceSetting.GetActiveProjectInfoApi;
                url += $"/{projectId}";
                var resultStr = await _httpClientService.GetAsync(url);
                var result = JsonConvert.DeserializeObject<BusinessResult<GetActiveProjectInfoResponse>>(resultStr);
                return result;
            }
            catch (Exception ex)
            {
                return new BusinessResult<GetActiveProjectInfoResponse>()
                {
                    Status = "error",
                    Message = $"创建任务失败：{ex.Message}",
                    Data = null
                };
            }
        }

        public async Task<string> WorkStateAsync(string baseUrl, int state)
        {
            try
            {
                if (string.IsNullOrEmpty(baseUrl))
                {
                    return "IsNullOrEmpty(baseUrl)";
                }

                if (!baseUrl.EndsWith("/"))
                    baseUrl = baseUrl + "/";
                var url = baseUrl + _serviceSetting.StateChangeApi + $"?state={state}";
                string result = string.Empty;
                result = await _httpClientService.GetAsync(url);
                var res = JsonConvert.DeserializeObject<StateChangeData.Response>(result);
                if (!res.Status.Equals("success"))
                {
                    //TODO V3.1 如果上传失败，则需要重新上传状态
                    _logService.LogError($"上报工作状态失败：{res.ToString()}");
                    return result;
                }
                return result;
            }
            catch (Exception ex)
            {
                _logService.LogError($"上报工作状态失败：{ex.ToString()}");
                return $"上报工作状态失败：{ex.ToString()}";
            }
        }

        public async Task<bool> TaskLogAsync(string baseUrl, TaskLogRequest taskLog)
        {
            if (!baseUrl.EndsWith("/"))
                baseUrl = baseUrl + "/";
            var url = baseUrl + _serviceSetting.TaskLogApi;
            var resultStr = await _httpClientService.PostAsync(url, JsonConvert.SerializeObject(taskLog));
            var result = JsonConvert.DeserializeObject<BusinessResult<string>>(resultStr);
            return result?.Code == 200;
        }

        public async Task<bool> UploadActivityLog(string baseUrl, string activityLogContent)
        {
            if (!baseUrl.EndsWith("/"))
                baseUrl = baseUrl + "/";
            var url = baseUrl + _serviceSetting.ActivityLogApi;
            string resStr = await _httpClientService.PostAsync(url, activityLogContent);
            var result = JsonConvert.DeserializeObject<BusinessResult<string>>(resStr);
            if ("success".Equals(result.Status))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}