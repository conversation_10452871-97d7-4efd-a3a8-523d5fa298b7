﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reactive;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Avalonia.Controls.Notifications;
using ReactiveUI;

// 移除 ReactiveUI.Validation 相关引用
using Skybridge.Domain.Core;
using Skybridge.Domain.Core.Enums;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Styles.Services;

namespace Skybridge.Robot.Presentation.ViewModels.SettingPage
{
    public class SettingViewModel : ViewModelBase
    {
        private const string URL_PATTERN =
            @"^(http|https):\/\/((?:(?:25[0-5]|2[0-4]\d|[01]?\d{1,2})\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d{1,2})|[a-zA-Z0-9-]+\.(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})(?::\d{1,5})?(\/[^?#]*)?(\?[^#]*)?(#.*)?$";

        private readonly Regex _urlRegex = new(URL_PATTERN, RegexOptions.Compiled);
        private readonly IBusinessRequestService _businessRequestService;
        private readonly string _robotId;
        private readonly EnvironmentConfig _environmentConfig;
        private readonly RobotContext _robotContext;

        private string? _appTitle = "QuickBot";
        private string _serverUrl = "";
        private string _robotName = "信创执行器";
        private bool _mode = true;
        private RobotTaskMode _selectedRobotTaskMode;

        public SettingViewModel(string robotId, IBusinessRequestService businessRequestService,
            EnvironmentConfig environmentConfig, RobotContext robotContext)
        {
            _robotId = robotId ?? throw new ArgumentNullException(nameof(robotId));
            _businessRequestService =
                businessRequestService ?? throw new ArgumentNullException(nameof(businessRequestService));
            _environmentConfig = environmentConfig;
            _robotContext = robotContext;
            SetupCommands();
            SetupValidation();
        }

        [Required(ErrorMessage = "应用名称不能为空")]
        public string? AppTitle
        {
            get => _appTitle;
            set => this.RaiseAndSetIfChanged(ref _appTitle, value);
        }

        public string ServerUrl
        {
            get => _serverUrl;
            set => this.RaiseAndSetIfChanged(ref _serverUrl, value?.Trim());
        }

        public string RobotName
        {
            get => _robotName;
            set => this.RaiseAndSetIfChanged(ref _robotName, value);
        }

        public bool Mode
        {
            get => _mode;
            set => this.RaiseAndSetIfChanged(ref _mode, value);
        }

        public RobotTaskMode SelectedRobotTaskMode
        {
            get => _selectedRobotTaskMode;
            set
            {
                this.RaiseAndSetIfChanged(ref _selectedRobotTaskMode, value);
                OnConfigChanged?.Invoke(true);
            }
        }

        private bool _serverUrlHasError;

        public bool ServerUrlHasError
        {
            get => _serverUrlHasError;
            set => this.RaiseAndSetIfChanged(ref _serverUrlHasError, value);
        }

        public ReactiveCommand<Unit, bool> OnChangeRobotName { get; private set; }
        public ReactiveCommand<Unit, bool> OnChangeMode { get; private set; }

        public ReactiveCommand<Unit, bool> OnChangeServerUrl { get; private set; }
        public Action<bool>? OnConfigChanged { get; set; }

        private void SetupCommands()
        {
            OnChangeRobotName = ReactiveCommand.CreateFromTask(ValidateRobotName);
            OnChangeMode = ReactiveCommand.CreateFromTask(HandleModeChange);
            OnChangeServerUrl = ReactiveCommand.CreateFromTask(ChangeServerUrl);
        }

        private async Task<bool> ChangeServerUrl()
        {
            //TODO 没有补充完整
            await Task.CompletedTask;
            return true;
        }

        // 添加错误字典
        private Dictionary<string, List<string>> _validationErrors = new Dictionary<string, List<string>>();

        private void SetupValidation()
        {
            // 手动验证
            this.WhenAnyValue(x => x.RobotName)
                .Subscribe(name => ValidateRobotName(name));

            this.WhenAnyValue(x => x.ServerUrl)
                .Subscribe(url => ValidateServerUrl(url));
        }

        private bool ValidateRobotName(string name)
        {
            bool isValid = !string.IsNullOrWhiteSpace(name);
            SetValidationError(nameof(RobotName), isValid ? null : "机器人名称不能为空.");
            return isValid;
        }

        private bool ValidateServerUrl(string url)
        {
            bool isValid = !string.IsNullOrWhiteSpace(url) && _urlRegex.IsMatch(url);
            SetValidationError(nameof(ServerUrl), isValid ? null : "服务器地址不能为空或格式错误.");
            return isValid;
        }

        private void SetValidationError(string propertyName, string error)
        {
            if (string.IsNullOrEmpty(error))
            {
                _validationErrors.Remove(propertyName);
            }
            else
            {
                _validationErrors[propertyName] = new List<string> { error };
            }

            if (propertyName == nameof(ServerUrl))
            {
                ServerUrlHasError = !string.IsNullOrEmpty(error);
            }
        }

        private IEnumerable<string> GetErrors(string propertyName)
        {
            return _validationErrors.TryGetValue(propertyName, out var errors) ? errors : Enumerable.Empty<string>();
        }

        private async Task<bool> ValidateRobotName()
        {
            return !GetErrors(nameof(RobotName)).OfType<string>().Any();
        }

        private async Task<bool> HandleModeChange()
        {
            Mode = !Mode;

            if (!Mode)
            {
                return await HandleModeOff();
            }

            return await HandleModeOn();
        }

        private async Task<bool> HandleModeOn()
        {
            if (GetErrors(nameof(ServerUrl)).OfType<string>().Any())
            {
                return await HandleInvalidServerUrl();
            }

            try
            {
                await _robotContext.RegisterRobot();
                await _robotContext.ChangeState(_robotContext.IsBusy ? 1 : 0); // 在线状态
                ShowSuccessMessage("服务器开启成功!");
                return true;
            }
            catch (Exception)
            {
                return await HandleServerConnectionError();
            }
        }

        private async Task<bool> HandleModeOff()
        {
            try
            {
                await _robotContext.ChangeState(2); // 离线状态
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置离线状态失败: {ex.Message}");
                return true; // 即使失败也返回true，因为我们仍然要关闭
            }
        }

        private async Task<bool> HandleInvalidServerUrl()
        {
            var result = await MessageBox.DialogResult("服务器地址不正确是否强制开启!");
            if (result)
            {
                ShowSuccessMessage("服务器开启成功!");
                return true;
            }

            Mode = false;
            return false;
        }

        private async Task<bool> HandleServerConnectionError()
        {
            var result = await MessageBox.DialogResult("服务器连接失败是否强制开启!");
            if (result)
            {
                return true;
            }

            Mode = false;
            return false;
        }

        private void ShowSuccessMessage(string message)
        {
            GlobalService.ShowMessage(new Avalonia.Controls.Notifications.Notification(
                "提示信息",
                message,
                NotificationType.Success));
        }
    }
}