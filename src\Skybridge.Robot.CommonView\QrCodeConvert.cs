﻿using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media.Imaging;
using Skybridge.Robot.Utils;

namespace Skybridge.Robot.Desktop.Views;

public class QrCodeConvert:IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        return value != null ? new Bitmap(value.ToString().ToQrCode()) : null;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        return null;
    }
}