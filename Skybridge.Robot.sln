﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34723.18
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Skybridge.Robot", "src\Skybridge.Robot\Skybridge.Robot.csproj", "{A7B45B1B-4AE9-474A-9C43-42813BCBDF6C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UI", "UI", "{E4DCBE57-053B-491F-890A-C447C0BE9ACF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Skybridge.Domain.Core", "src\Skybridge.Domain.Core\Skybridge.Domain.Core.csproj", "{539AB1B1-0732-4291-87EF-5F25648D7B32}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Skybridge.Robot.Styles", "src\Skybridge.Robot.Styles\Skybridge.Robot.Styles.csproj", "{9A8107E3-16E4-423D-AC2F-FA3B393E6E3B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Skybridge.Robot.Utils", "src\Skybridge.Robot.Utils\Skybridge.Robot.Utils.csproj", "{C78E7D61-964B-4B63-B4C9-0CFC4CAB5934}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Skybridge.Robot.License", "src\Skybridge.Robot.License\Skybridge.Robot.License.csproj", "{259126C8-A0E3-4EA5-82FD-0F01DB8F0772}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Skybridge.Robot.Private", "src\Skybridge.Robot.Private\Skybridge.Robot.Private.csproj", "{0592E877-F21B-447C-BB67-B956929B98E0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Skybridge.Robot.CommonView", "src\Skybridge.Robot.CommonView\Skybridge.Robot.CommonView.csproj", "{9DDDF31B-AEDE-4943-ADB0-66C44A789BA3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Skybridge.Controls", "..\CommonCore\src\Skybridge.Controls\Skybridge.Controls.csproj", "{E83560F5-6E3A-416C-B1A0-161BF55F450C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Skybridge.Shared", "..\CommonCore\src\Skybridge.Shared\Skybridge.Shared.csproj", "{F989DAB1-043F-412A-A10E-53533451F629}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Skybridge.Theme", "..\CommonCore\src\Skybridge.Theme\Skybridge.Theme.csproj", "{DBD3254F-E72D-4600-B047-C70363CBCDCC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Theme", "Theme", "{63AD6C08-1BBA-4A2B-9449-E61A7E4EBC53}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{7202EA41-4788-4DB4-A368-A2E15E2D5823}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Skybridge.Robot.Infrastructure", "src\Skybridge.Robot.Infrastructure\Skybridge.Robot.Infrastructure.csproj", "{E7ED81D5-1E82-4D60-9E32-3FE4E54D56BA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Skybridge.Robot.Application", "src\Skybridge.Robot.Application\Skybridge.Robot.Application.csproj", "{8A9AB089-178B-464A-AA79-6229F0FE2145}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Skybridge.Robot.WebServer", "src\Skybridge.Robot.WebServer\Skybridge.Robot.WebServer.csproj", "{F07F2BCA-FEF7-4BBA-9D52-96790A34D117}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Skybridge.Robot.Presentation", "src\Skybridge.Robot.Presentation\Skybridge.Robot.Presentation.csproj", "{26D8256D-0BAB-D7EE-77C2-E9EFC6B1A64A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A7B45B1B-4AE9-474A-9C43-42813BCBDF6C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7B45B1B-4AE9-474A-9C43-42813BCBDF6C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7B45B1B-4AE9-474A-9C43-42813BCBDF6C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7B45B1B-4AE9-474A-9C43-42813BCBDF6C}.Release|Any CPU.Build.0 = Release|Any CPU
		{539AB1B1-0732-4291-87EF-5F25648D7B32}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{539AB1B1-0732-4291-87EF-5F25648D7B32}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{539AB1B1-0732-4291-87EF-5F25648D7B32}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{539AB1B1-0732-4291-87EF-5F25648D7B32}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A8107E3-16E4-423D-AC2F-FA3B393E6E3B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A8107E3-16E4-423D-AC2F-FA3B393E6E3B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A8107E3-16E4-423D-AC2F-FA3B393E6E3B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A8107E3-16E4-423D-AC2F-FA3B393E6E3B}.Release|Any CPU.Build.0 = Release|Any CPU
		{C78E7D61-964B-4B63-B4C9-0CFC4CAB5934}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C78E7D61-964B-4B63-B4C9-0CFC4CAB5934}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C78E7D61-964B-4B63-B4C9-0CFC4CAB5934}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C78E7D61-964B-4B63-B4C9-0CFC4CAB5934}.Release|Any CPU.Build.0 = Release|Any CPU
		{259126C8-A0E3-4EA5-82FD-0F01DB8F0772}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{259126C8-A0E3-4EA5-82FD-0F01DB8F0772}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{259126C8-A0E3-4EA5-82FD-0F01DB8F0772}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{259126C8-A0E3-4EA5-82FD-0F01DB8F0772}.Release|Any CPU.Build.0 = Release|Any CPU
		{0592E877-F21B-447C-BB67-B956929B98E0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0592E877-F21B-447C-BB67-B956929B98E0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0592E877-F21B-447C-BB67-B956929B98E0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0592E877-F21B-447C-BB67-B956929B98E0}.Release|Any CPU.Build.0 = Release|Any CPU
		{9DDDF31B-AEDE-4943-ADB0-66C44A789BA3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9DDDF31B-AEDE-4943-ADB0-66C44A789BA3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9DDDF31B-AEDE-4943-ADB0-66C44A789BA3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9DDDF31B-AEDE-4943-ADB0-66C44A789BA3}.Release|Any CPU.Build.0 = Release|Any CPU
		{E83560F5-6E3A-416C-B1A0-161BF55F450C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E83560F5-6E3A-416C-B1A0-161BF55F450C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E83560F5-6E3A-416C-B1A0-161BF55F450C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E83560F5-6E3A-416C-B1A0-161BF55F450C}.Release|Any CPU.Build.0 = Release|Any CPU
		{F989DAB1-043F-412A-A10E-53533451F629}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F989DAB1-043F-412A-A10E-53533451F629}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F989DAB1-043F-412A-A10E-53533451F629}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F989DAB1-043F-412A-A10E-53533451F629}.Release|Any CPU.Build.0 = Release|Any CPU
		{DBD3254F-E72D-4600-B047-C70363CBCDCC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DBD3254F-E72D-4600-B047-C70363CBCDCC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DBD3254F-E72D-4600-B047-C70363CBCDCC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DBD3254F-E72D-4600-B047-C70363CBCDCC}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7ED81D5-1E82-4D60-9E32-3FE4E54D56BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7ED81D5-1E82-4D60-9E32-3FE4E54D56BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7ED81D5-1E82-4D60-9E32-3FE4E54D56BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7ED81D5-1E82-4D60-9E32-3FE4E54D56BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A9AB089-178B-464A-AA79-6229F0FE2145}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A9AB089-178B-464A-AA79-6229F0FE2145}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A9AB089-178B-464A-AA79-6229F0FE2145}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A9AB089-178B-464A-AA79-6229F0FE2145}.Release|Any CPU.Build.0 = Release|Any CPU
		{F07F2BCA-FEF7-4BBA-9D52-96790A34D117}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F07F2BCA-FEF7-4BBA-9D52-96790A34D117}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F07F2BCA-FEF7-4BBA-9D52-96790A34D117}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F07F2BCA-FEF7-4BBA-9D52-96790A34D117}.Release|Any CPU.Build.0 = Release|Any CPU
		{26D8256D-0BAB-D7EE-77C2-E9EFC6B1A64A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{26D8256D-0BAB-D7EE-77C2-E9EFC6B1A64A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{26D8256D-0BAB-D7EE-77C2-E9EFC6B1A64A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{26D8256D-0BAB-D7EE-77C2-E9EFC6B1A64A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A7B45B1B-4AE9-474A-9C43-42813BCBDF6C} = {7202EA41-4788-4DB4-A368-A2E15E2D5823}
		{539AB1B1-0732-4291-87EF-5F25648D7B32} = {7202EA41-4788-4DB4-A368-A2E15E2D5823}
		{9A8107E3-16E4-423D-AC2F-FA3B393E6E3B} = {63AD6C08-1BBA-4A2B-9449-E61A7E4EBC53}
		{C78E7D61-964B-4B63-B4C9-0CFC4CAB5934} = {7202EA41-4788-4DB4-A368-A2E15E2D5823}
		{259126C8-A0E3-4EA5-82FD-0F01DB8F0772} = {7202EA41-4788-4DB4-A368-A2E15E2D5823}
		{0592E877-F21B-447C-BB67-B956929B98E0} = {7202EA41-4788-4DB4-A368-A2E15E2D5823}
		{9DDDF31B-AEDE-4943-ADB0-66C44A789BA3} = {E4DCBE57-053B-491F-890A-C447C0BE9ACF}
		{E83560F5-6E3A-416C-B1A0-161BF55F450C} = {63AD6C08-1BBA-4A2B-9449-E61A7E4EBC53}
		{F989DAB1-043F-412A-A10E-53533451F629} = {63AD6C08-1BBA-4A2B-9449-E61A7E4EBC53}
		{DBD3254F-E72D-4600-B047-C70363CBCDCC} = {63AD6C08-1BBA-4A2B-9449-E61A7E4EBC53}
		{E7ED81D5-1E82-4D60-9E32-3FE4E54D56BA} = {7202EA41-4788-4DB4-A368-A2E15E2D5823}
		{8A9AB089-178B-464A-AA79-6229F0FE2145} = {7202EA41-4788-4DB4-A368-A2E15E2D5823}
		{F07F2BCA-FEF7-4BBA-9D52-96790A34D117} = {7202EA41-4788-4DB4-A368-A2E15E2D5823}
		{26D8256D-0BAB-D7EE-77C2-E9EFC6B1A64A} = {7202EA41-4788-4DB4-A368-A2E15E2D5823}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A283E001-D272-4BDB-83A7-A72B961A7EC7}
	EndGlobalSection
EndGlobal
