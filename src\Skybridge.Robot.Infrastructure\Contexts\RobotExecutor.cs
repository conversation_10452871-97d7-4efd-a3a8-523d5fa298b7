﻿using System.Diagnostics;
using Skybridge.Robot.Application.Interfaces;

namespace Skybridge.Robot.Infrastructure.Contexts
{
    public sealed class RobotExecutor : IDisposable
    {
        private readonly ILogService _logService;
        private Process? _process;
        private bool _disposed;

        /// <summary>
        /// 当执行引擎退出时触发的事件
        /// </summary>
        public event Action<string>? OnExit;

        /// <summary>
        /// 获取执行器是否正在运行
        /// </summary>
        public bool IsRunning => _process != null && !_process.HasExited;

        public RobotExecutor(ILogService logService)
        {
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
        }

        /// <summary>
        /// 执行机器人任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="targetFile">目标文件路径</param>
        /// <param name="base64">Base64编码的任务参数</param>
        public void Execute(string taskId, string targetFile, string base64)
        {
            ArgumentNullException.ThrowIfNull(taskId);
            ArgumentNullException.ThrowIfNull(targetFile);
            ArgumentNullException.ThrowIfNull(base64);

            try
            {
                _process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = targetFile,
                        Arguments = base64,
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true,
                        WorkingDirectory = Path.GetDirectoryName(targetFile)
                    }
                };

                _process.OutputDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        Console.WriteLine($"Task {taskId} output: {e.Data}");
                    }
                };

                _process.ErrorDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        _logService.LogError($"Task {taskId} error: {e.Data}");
                    }
                };

                _process.Exited += (sender, e) =>
                {
                    _logService.LogInfo($"Task {taskId} exited with code: {_process.ExitCode}");
                    OnExit?.Invoke(taskId);
                };
                _process.EnableRaisingEvents = true;
                _process.Start();

                _process.BeginOutputReadLine();
                _process.BeginErrorReadLine();
            }
            catch (Exception ex)
            {
                _logService.LogError($"Failed to execute task {taskId}",ex);
                OnExit?.Invoke(taskId);
            }
        }

        /// <summary>
        /// 强制终止执行器
        /// </summary>
        public void Kill()
        {
            try
            {
                if (_process != null && !_process.HasExited)
                {
                    _process.Kill();
                    _process.WaitForExit(5000); // 等待最多5秒
                }
            }
            catch (Exception ex)
            {
                _logService.LogError("Error killing process",ex);
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                Kill();
                _process?.Dispose();
            }
            catch (Exception ex)
            {
                _logService.LogError("Error during disposal",ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}