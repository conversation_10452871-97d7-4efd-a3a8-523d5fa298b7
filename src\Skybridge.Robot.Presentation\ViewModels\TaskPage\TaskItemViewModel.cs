﻿using ReactiveUI;
using Skybridge.Domain.Core.Exector;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive;
using System.Text;
using System.Threading.Tasks;
using Skybridge.Robot.Styles.Services;

namespace Skybridge.Robot.Presentation.ViewModels.TaskPage
{
    public class TaskItemViewModel : ViewModelBase
    {
        public string Id { get; set; }
        private string _name;

        public string Name
        {
            get => _name;
            set => this.RaiseAndSetIfChanged(ref _name, value);
        }

        private DateTime _startTime;

        public DateTime StartTime
        {
            get => _startTime;
            set => this.RaiseAndSetIfChanged(ref _startTime, value);
        }

        private string _version;
        private readonly IExecManager _execManager;

        public TaskItemViewModel(IExecManager execManager) => _execManager = execManager;

        public string Version
        {
            get => _version;
            set => this.RaiseAndSetIfChanged(ref _version, value);
        }

        public ReactiveCommand<string, Unit> StopTaskCommand => ReactiveCommand.Create<string>(StopTask);

        private async void StopTask(string taskId)
        {
            var result = await MessageBox.DialogResult("是否停止任务");
            if (!result) return;
            await _execManager.StopExecutorAsync(taskId, new Domain.Core.Models.TaskStopInfo("界面手动停止任务", Domain.Core.Enums.TaskStopType.RemoteStop));
        }
    }
}