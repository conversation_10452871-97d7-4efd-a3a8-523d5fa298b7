﻿using Newtonsoft.Json;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Model;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Utils;
using System.IO.Compression;
using System.Text;
using Skybridge.Robot.Infrastructure.Utilities;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public class ProjectManager : IProjectManager
    {
        private readonly IFileStorageConfig _fileStorageConfig;
        private readonly JsonDbContext _dbContext;
        private readonly RunningContext _runningContext;


        public ProjectManager(IFileStorageConfig fileStorageConfig, JsonDbContext dbContext,RunningContext runningContext)
        {
            _runningContext = runningContext;
            _fileStorageConfig = fileStorageConfig;
            _dbContext = dbContext;
        }

        public IEnumerable<ArgumentModel>? GetProjectRunArguments(string pId)
        {
            var project = _runningContext.RobotProjects.Find(x => x.Id.Equals(pId));
            if (project is null || !Directory.Exists(project.FilePath))
            {
                Console.WriteLine("项目或项目文件不存在");
                return null;
            }
            var paramtersStr = File.ReadAllText(Path.Combine(project.FilePath, "arguments.json"));
            var argumentsValues = paramtersStr.To<List<ArgumentModel>>();
            return argumentsValues;
        }


        public IEnumerable<(RobotProject, IEnumerable<Tuple<string, string>>)> LoadList()
        {
            return _runningContext.RobotProjects
                .OrderBy(x => x.Version)
                .GroupBy(x => new { x.IsRemote, x.Name })
                .Select(x => (x.Last(), x.Select(y => Tuple.Create<string, string>(y.Id, y.Version))));
        }

        public void DelProject(string id)
        {
            var project = _dbContext.Get<RobotProject>()
                .FirstOrDefault(x => x.Id.Equals(id));
            if (project == null) return;
            _dbContext.Delete<RobotProject>(id);
            _runningContext.RobotProjects.Remove(project);
            if (Directory.Exists(project.FilePath)) Directory.Delete(project.FilePath, true);
        }

        public void ChangeArguments(string projectName, string version, IEnumerable<ArgumentModel> arguments)
        {
            var project = _runningContext.RobotProjects.Find(x => x.Name == projectName && x.Version == version);
            if (project is null)
                throw new Exception("项目不存在");
            var parameterStr = JsonConvert.SerializeObject(arguments);
            File.WriteAllText(Path.Combine(project.FilePath, "arguments.json"), parameterStr);
        }
    }
}