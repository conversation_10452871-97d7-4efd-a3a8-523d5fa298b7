﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia.Controls;
using Skybridge.Domain.Core;
using Skybridge.Domain.Core.Config;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Contexts;

namespace Skybridge.Robot.Presentation.ViewModels.SettingPage
{
    public class SettingPageImpl : IPage
    {
        private readonly IAppConfigManager _appConfigManager;
        private readonly SettingViewModel _settingViewModel;

        public SettingPageImpl(IAppConfigManager appConfigManager, IBusinessRequestService bussinessRequestService, EnvironmentConfig environmentConfig, RobotContext robotContext)
        {
            _appConfigManager = appConfigManager;
            _settingViewModel = new SettingViewModel(appConfigManager.RobotConfig.RobotId, bussinessRequestService, environmentConfig, robotContext);
            _settingViewModel.OnChangeRobotName.Subscribe(SaveRobotName);
            _settingViewModel.OnChangeMode.Subscribe(SaveConfig);
            _settingViewModel.OnConfigChanged += SaveConfig;

            Load();
            View = new SettingView
            {
                DataContext = _settingViewModel
            };
        }

        private void SaveRobotName(bool isExec)
        {
            if (!isExec) return;
            _appConfigManager.Save(_appConfigManager.RobotConfig.ServerUrl,  _settingViewModel.RobotName, _appConfigManager.RobotConfig.Mode == Mode.RemoteServer, _appConfigManager.RobotConfig.RobotTaskMode);
        }

        private void SaveConfig(bool isExec)
        {
            if (!isExec) return;
            _appConfigManager.Save(_settingViewModel.ServerUrl, _settingViewModel.RobotName, _settingViewModel.Mode, _settingViewModel.SelectedRobotTaskMode);
        }

        public string Name => "设置";

        public UserControl View { get; }

        public void Load()
        {
            _settingViewModel.ServerUrl = _appConfigManager.RobotConfig.ServerUrl;
            _settingViewModel.Mode = _appConfigManager.RobotConfig.Mode.Equals(Mode.RemoteServer);
            _settingViewModel.RobotName = _appConfigManager.RobotConfig.RobotName;
            _settingViewModel.SelectedRobotTaskMode = _appConfigManager.RobotConfig.RobotTaskMode;
        }

        public int Order => 3;
        public string Icon => "rti-setting";
    }
}