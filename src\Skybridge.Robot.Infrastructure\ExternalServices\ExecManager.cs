﻿using System.Net.NetworkInformation;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Enums;
using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Contexts;

namespace Skybridge.Robot.Infrastructure.ExternalServices
{
    public sealed class ExecManager : IExecManager
    {
        private readonly IRobotWebServer _robotWebServer;
        private readonly IDictionary<string, RobotExecutor> _robotExecutors;
        private readonly IAppConfigManager _appConfigManager;
        private readonly ITaskManager _taskManager;
        private readonly ILogService _logService;
        private readonly IRobotChannel _robotChannel;
        private readonly RunningContext _runningContext;
        private readonly SemaphoreSlim _executorLock;
        private readonly CancellationTokenSource _disposeCts;
        private bool _disposed;

        private event Action<RobotTask>? OnCompleted;

        private event Action<int>? OnRunningCountChanged;

        public event Action<object>? OnLogReceived;

        public int ReferenceCount { get; private set; }
        private int ListenPort { get; set; }

        public ExecManager(
            IAppConfigManager appConfigManager,
            ITaskManager taskManager,
            ILogService logService,
            IRobotWebServer robotWebServer,
            RunningContext runningContext
            )
        {
            _appConfigManager = appConfigManager ?? throw new ArgumentNullException(nameof(appConfigManager));
            _taskManager = taskManager ?? throw new ArgumentNullException(nameof(taskManager));
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
            _robotWebServer = robotWebServer ?? throw new ArgumentNullException(nameof(robotWebServer));
            _runningContext = runningContext ?? throw new ArgumentException(nameof(runningContext));
            _robotExecutors = new Dictionary<string, RobotExecutor>();
            _executorLock = new SemaphoreSlim(1, 1);
            _disposeCts = new CancellationTokenSource();

        }

        public  void InitializeServer()
        {
            ListenPort = FindAvailablePort(_appConfigManager.RobotConfig.ListenPort);
            _robotWebServer.StartServer(ListenPort);
        }

        private int FindAvailablePort(int startPort)
        {
            var ipGlobalProperties = IPGlobalProperties.GetIPGlobalProperties();
            var tcpConnInfoArray = ipGlobalProperties.GetActiveTcpListeners();

            while (tcpConnInfoArray.Any(endPoint => endPoint.Port == startPort))
            {
                startPort++;
            }

            return startPort;
        }


        public async Task<bool> StartExecutorAsync(StartOption option)
        {
            ThrowIfDisposed();
            ArgumentNullException.ThrowIfNull(option);

            await _executorLock.WaitAsync(_disposeCts.Token);
            try
            {
                var robotExecutor = new RobotExecutor(_logService);

                var base64 = _taskManager.GenerateBase64(
                    ListenPort,
                    option.TaskId,
                    option.ServerTaskId,
                    option.ProjectId,
                    option.BaseUrl,
                    option.Token,
                    option.InParameters,
                    option.IsLogDebug);

                _robotExecutors.Add(option.TaskId, robotExecutor);
                ReferenceCount++;
                OnRunningCountChanged?.Invoke(ReferenceCount);
                robotExecutor.OnExit += async (taskId) =>
                {
                    var robotTask = _runningContext.RobotTasks.FirstOrDefault(d => d.Value.Id.Equals(taskId))
                        .Value;
                    if (robotTask == null)
                    {
                        _logService.LogWarning($"robotTask not found for RobotTasks: {taskId}");
                        return;
                    }

                    if (!robotTask.IsReceivedLog && robotTask.TaskStopInfo == null)
                    {
                        await Task.Delay(3000); // 等待日志接收
                        robotTask.TaskStopInfo = new TaskStopInfo("执行引擎异常退出", TaskStopType.ExceptionStop);
                        _logService.LogWarning($"Executor exited unexpectedly for task: {taskId}");
                    }

                    OnRunningCountChanged?.Invoke(ReferenceCount);
                    OnCompleted?.Invoke(robotTask);
                    _runningContext.OnTaskCompleted?.Invoke(robotTask);
                };
                robotExecutor.Execute(option.TaskId, option.TargetFile, base64);

                _logService.LogInfo($"Started executor for task: {option.TaskId}");
                return true;
            }
            catch (Exception ex)
            {
                _logService.LogError( $"Failed to start executor for task: {option.TaskId}",ex);
                return false;
            }
            finally
            {
                _executorLock.Release();
            }
        }
        
        
        

        public async Task<bool> StopExecutorAsync(string taskId, TaskStopInfo taskStopInfo)
        {
            ThrowIfDisposed();
            ArgumentNullException.ThrowIfNull(taskId);

            await _executorLock.WaitAsync(_disposeCts.Token);
            try
            {
                var robotTask = _runningContext.RobotTasks.FirstOrDefault(d => d.Value.Id.Equals(taskId)).Value;
                if (robotTask == null)
                {
                    return false;
                }
                if (robotTask.IsStopped)
                {
                    _logService.LogWarning($"robotTask has been stopped: {taskId}");
                    return false;
                }
                if (!_robotExecutors.TryGetValue(taskId, out var executor))
                {
                    _logService.LogWarning($"Executor not found for task: {taskId}");
                    return false;
                }

                executor.Kill();
                _robotExecutors.Remove(taskId);
                robotTask.IsStopped = true;
                ReferenceCount--;
                robotTask.IsStopped = true;
                robotTask.TaskStopInfo = taskStopInfo;
                OnRunningCountChanged?.Invoke(ReferenceCount);
                OnCompleted?.Invoke(robotTask);
                _runningContext.OnTaskCompleted?.Invoke(robotTask);
                _logService.LogInfo($"Stopped executor for task: {taskId}");
                return true;
            }
            catch (Exception ex)
            {
                _logService.LogError($"Failed to stop executor for task: {taskId}",ex);
                return false;
            }
            finally
            {
                _executorLock.Release();
            }
        }


        public async Task<ExecutorStatus> GetExecutorStatusAsync(string taskId)
        {
            ThrowIfDisposed();
            ArgumentNullException.ThrowIfNull(taskId);

            await _executorLock.WaitAsync(_disposeCts.Token);
            try
            {
                if (!_robotExecutors.TryGetValue(taskId, out var executor))
                {
                    return ExecutorStatus.NotFound;
                }

                return executor.IsRunning ? ExecutorStatus.Running : ExecutorStatus.Stopped;
            }
            finally
            {
                _executorLock.Release();
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(ExecManager));
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposeCts.Cancel();
            try
            {
                // 停止所有执行器
                foreach (var executor in _robotExecutors.Values)
                {
                    executor.Kill();
                }
                _robotExecutors.Clear();

                // 清理资源
                _robotWebServer.StopServer();
                _executorLock.Dispose();
                _disposeCts.Dispose();
            }
            catch (Exception ex)
            {
                _logService.LogError("Error during ExecManager disposal",ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}