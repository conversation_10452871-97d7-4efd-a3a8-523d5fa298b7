﻿using System.Collections.ObjectModel;
using ReactiveUI;

namespace Skybridge.Robot.Presentation.ViewModels.ProjectPage
{
    public class ProjectViewModel : ViewModelBase
    {
        private ObservableCollection<ProjectItemViewModel> _projectItems = new();

        public ObservableCollection<ProjectItemViewModel> ProjectItems
        {
            get => _projectItems;
            set => this.RaiseAndSetIfChanged(ref _projectItems, value);
        }
    }
}