﻿using System;
using System.IO;

namespace Skybridge.Domain.Core.Config;

public class FileStorageConfigBase : IFileStorageConfig
{
    public string BasePath
    {
        get
        {
            var dir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".robot");
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".robot");
        }
    }

    public string ProjectBasePath => Path.Combine(BasePath, "projects");

    private string LogBasePath
    {
        get
        {
            var dir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".robotlogs");
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".robotlogs");
        }
    }

    public string ApplicationPath => AppDomain.CurrentDomain.BaseDirectory;

    public string PagePath => Path.Combine(BasePath, "Pages");

    public string PluginPath => Path.Combine(BasePath, "Plugins");

    public string ProjectPath => Path.Combine(BasePath, "Projects");

    public string LogPath => Path.Combine(ApplicationPath, "Logs");

    public string TempPath => Path.Combine(BasePath, "Temps");

    public string AppVersionPath => Path.Combine(ApplicationPath, "appVersion.json");
}